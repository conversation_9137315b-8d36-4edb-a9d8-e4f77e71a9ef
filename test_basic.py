#!/usr/bin/env python3
"""
Basic test of Version 1 functionality
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.append("src")

def test_ontology_models():
    """Test ontology models"""
    print("🧪 Testing Ontology Models...")
    
    try:
        from ontology.models import EntityType, RelationshipType, EntityRelationshipTriple
        
        # Test entity types
        entity_types = [e.value for e in EntityType]
        print(f"✅ Entity types loaded: {entity_types}")
        
        # Test relationship types  
        relationship_types = [r.value for r in RelationshipType]
        print(f"✅ Relationship types loaded: {relationship_types}")
        
        # Test creating a triple
        triple = EntityRelationshipTriple(
            entity_subject="<PERSON>",
            relationship_type=RelationshipType.WORKS_FOR,
            entity_object="TechCorp",
            entity_subject_type=EntityType.PERSON,
            entity_object_type=EntityType.ORGANIZATION,
            source_text_snippet="<PERSON> works at TechCorp",
            relationship_context="Employment relationship",
            document_id="TEST_001",
            entity_subject_id="sarah_johnson_001",
            entity_object_id="techcorp_001"
        )
        
        print(f"✅ Created triple: {triple.entity_subject} --[{triple.relationship_type}]--> {triple.entity_object}")
        return True
        
    except Exception as e:
        print(f"❌ Ontology models test failed: {str(e)}")
        return False

def test_quality_metrics():
    """Test quality metrics without LLM"""
    print("\n📊 Testing Quality Metrics...")
    
    try:
        from validation.quality_metrics import QualityMetrics
        from ontology.models import EntityRelationshipTriple, ExtractionResult, EntityType, RelationshipType
        
        # Create sample triples
        triples = [
            EntityRelationshipTriple(
                entity_subject="Sarah Johnson",
                relationship_type=RelationshipType.WORKS_FOR,
                entity_object="TechCorp",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.ORGANIZATION,
                source_text_snippet="Sarah Johnson works at TechCorp",
                relationship_context="Employment relationship",
                document_id="TEST_001",
                entity_subject_id="sarah_johnson_001",
                entity_object_id="techcorp_001"
            ),
            EntityRelationshipTriple(
                entity_subject="Sarah Johnson",
                relationship_type=RelationshipType.HOLDS_ROLE,
                entity_object="VP of Engineering",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.ROLE,
                source_text_snippet="Sarah Johnson, VP of Engineering",
                relationship_context="Role assignment",
                document_id="TEST_001",
                entity_subject_id="sarah_johnson_001",
                entity_object_id="vp_engineering_001"
            )
        ]
        
        # Create extraction result
        result = ExtractionResult(
            triples=triples,
            document_id="TEST_001",
            total_triples=len(triples)
        )
        
        # Test quality metrics
        quality_calc = QualityMetrics()
        metrics = quality_calc.calculate_basic_metrics(result)
        
        print(f"✅ Basic metrics calculated:")
        print(f"   - Total triples: {metrics['total_triples']}")
        print(f"   - Average confidence: {metrics['avg_confidence']:.2f}")
        print(f"   - Entity types: {list(metrics['entity_types_coverage'].keys())}")
        print(f"   - Relationship types: {list(metrics['relationship_types_coverage'].keys())}")
        
        # Test validation
        validation = quality_calc.validate_extraction_result(result)
        print(f"✅ Validation completed:")
        print(f"   - Is valid: {validation['is_valid']}")
        print(f"   - Issues: {validation['total_issues']}")
        print(f"   - Warnings: {validation['total_warnings']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quality metrics test failed: {str(e)}")
        return False

def test_sample_data():
    """Test sample data loading"""
    print("\n📄 Testing Sample Data...")
    
    try:
        sample_file = Path("data/samples/sample_texts.json")
        
        if sample_file.exists():
            with open(sample_file, "r") as f:
                sample_data = json.load(f)
            
            print(f"✅ Sample data loaded: {len(sample_data)} samples")
            for i, sample in enumerate(sample_data[:3]):  # Show first 3
                print(f"   - {sample['document_id']}: {sample['text'][:50]}...")
            
            return True
        else:
            print(f"❌ Sample data file not found: {sample_file}")
            return False
            
    except Exception as e:
        print(f"❌ Sample data test failed: {str(e)}")
        return False

def test_memgraph_connection():
    """Test Memgraph connection (if available)"""
    print("\n🔗 Testing Memgraph Connection...")
    
    try:
        # Check if pymgclient is available
        import pymgclient
        print("✅ pymgclient module found")
        
        from graph_construction.memgraph_client import MemgraphClient
        
        # Try to connect
        client = MemgraphClient()
        client.connect()
        
        # Test basic query
        result = client.execute_query("RETURN 1 as test")
        print(f"✅ Memgraph connection successful!")
        print(f"   - Query result: {result}")
        
        client.disconnect()
        return True
        
    except ImportError:
        print("❌ pymgclient not installed - skipping Memgraph test")
        print("   - Install with: pip install pymgclient")
        return False
    except Exception as e:
        print(f"❌ Memgraph connection failed: {str(e)}")
        print("   - Make sure Memgraph is running: docker run -p 7687:7687 memgraph/memgraph")
        return False

def main():
    """Run basic tests"""
    print("🚀 Starting Basic Tests...")
    print("=" * 50)
    
    tests = [
        ("Ontology Models", test_ontology_models),
        ("Quality Metrics", test_quality_metrics),
        ("Sample Data", test_sample_data),
        ("Memgraph Connection", test_memgraph_connection)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"   - {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🏆 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All basic tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()