#!/usr/bin/env python3
"""
Test JSON repair functionality for malformed LLM responses
"""

import sys
import json

# Add src to path
sys.path.append("src")

from core.pipeline import _repair_json_string


def test_json_repair():
    """Test various JSON repair scenarios"""
    
    print("🧪 Testing JSON Repair Functionality")
    print("=" * 50)
    
    # Test case 1: Missing comma between objects
    malformed1 = '[{"a": 1} {"b": 2}]'
    expected1 = '[{"a": 1}, {"b": 2}]'
    
    print(f"Test 1 - Missing comma between objects:")
    print(f"  Input:    {malformed1}")
    repaired1 = _repair_json_string(malformed1)
    print(f"  Repaired: {repaired1}")
    print(f"  Expected: {expected1}")
    
    try:
        parsed = json.loads(repaired1)
        print(f"  ✅ Valid JSON: {parsed}")
    except json.JSONDecodeError as e:
        print(f"  ❌ Still invalid: {e}")
    
    print()
    
    # Test case 2: Trailing comma
    malformed2 = '{"triples": [{"a": 1}, {"b": 2},]}'
    expected2 = '{"triples": [{"a": 1}, {"b": 2}]}'
    
    print(f"Test 2 - Trailing comma:")
    print(f"  Input:    {malformed2}")
    repaired2 = _repair_json_string(malformed2)
    print(f"  Repaired: {repaired2}")
    print(f"  Expected: {expected2}")
    
    try:
        parsed = json.loads(repaired2)
        print(f"  ✅ Valid JSON: {parsed}")
    except json.JSONDecodeError as e:
        print(f"  ❌ Still invalid: {e}")
    
    print()
    
    # Test case 3: Single quotes
    malformed3 = "{'triples': [{'entity_subject': 'Sarah'}]}"
    expected3 = '{"triples": [{"entity_subject": "Sarah"}]}'
    
    print(f"Test 3 - Single quotes:")
    print(f"  Input:    {malformed3}")
    repaired3 = _repair_json_string(malformed3)
    print(f"  Repaired: {repaired3}")
    print(f"  Expected: {expected3}")
    
    try:
        parsed = json.loads(repaired3)
        print(f"  ✅ Valid JSON: {parsed}")
    except json.JSONDecodeError as e:
        print(f"  ❌ Still invalid: {e}")
    
    print()
    
    # Test case 4: Complex malformed JSON (similar to LLM output)
    malformed4 = '''[
    {
        "entity_subject": "Sarah Johnson",
        "relationship_type": "works_at",
        "entity_object": "TechCorp"
    }
    {
        "entity_subject": "meeting",
        "relationship_type": "located_in",
        "entity_object": "Conference Room A",
    }
]'''
    
    print(f"Test 4 - Complex malformed JSON:")
    print(f"  Input:    {malformed4}")
    repaired4 = _repair_json_string(malformed4)
    print(f"  Repaired: {repaired4}")
    
    try:
        parsed = json.loads(repaired4)
        print(f"  ✅ Valid JSON with {len(parsed)} items")
        for i, item in enumerate(parsed):
            print(f"    Item {i+1}: {item}")
    except json.JSONDecodeError as e:
        print(f"  ❌ Still invalid: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 JSON Repair Test Complete")


if __name__ == "__main__":
    test_json_repair()
