#!/usr/bin/env python3
"""
Ontology Generation Test
========================

Standalone test that loads a Cypher ontology file and shows the 3 generated formats:
1. Original Cypher content
2. Human-readable prompt text (for LLM instruction)
3. Pydantic enums (for validation)
4. JSON schema (for function calling)

Usage:
    python dev_tests/test_ontology_generation.py                    # Uses test ontology
    python dev_tests/test_ontology_generation.py path/to/file.cypher # Uses custom ontology
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.append("src")

from ontology.loader import load_ontology_from_cypher
from ontology.generators import generate_prompt_ontology, generate_pydantic_enums, generate_function_schema


def pretty_print_cypher(cypher_file_path: str):
    """Pretty print the original Cypher content"""
    print("=" * 80)
    print("1️⃣ ORIGINAL CYPHER ONTOLOGY")
    print("=" * 80)
    
    try:
        with open(cypher_file_path, 'r', encoding='utf-8') as f:
            cypher_content = f.read()
        
        print(f"📁 File: {cypher_file_path}")
        print(f"📏 Size: {len(cypher_content)} characters")
        print()
        print(cypher_content)
        
    except Exception as e:
        print(f"❌ Error reading Cypher file: {e}")
        return False
    
    return True


def pretty_print_prompt_ontology(ontology: dict):
    """Pretty print the generated prompt ontology"""
    print("\n" + "=" * 80)
    print("2️⃣ GENERATED PROMPT ONTOLOGY (for LLM instruction)")
    print("=" * 80)
    
    try:
        prompt_text = generate_prompt_ontology(ontology)
        
        print(f"📊 Entities: {len(ontology['entities'])}")
        print(f"🔗 Relationships: {len(ontology['relationships'])}")
        print(f"📏 Prompt length: {len(prompt_text)} characters")
        print()
        print(prompt_text)
        
    except Exception as e:
        print(f"❌ Error generating prompt ontology: {e}")


def pretty_print_pydantic_enums(ontology: dict):
    """Pretty print the generated Pydantic enums"""
    print("\n" + "=" * 80)
    print("3️⃣ GENERATED PYDANTIC ENUMS (for validation)")
    print("=" * 80)
    
    try:
        EntityType, RelationshipType = generate_pydantic_enums(ontology)
        
        print(f"🏷️  EntityType enum with {len(EntityType)} members:")
        for member in EntityType:
            print(f"   {member.name} = \"{member.value}\"")
        
        print(f"\n🔗 RelationshipType enum with {len(RelationshipType)} members:")
        for member in RelationshipType:
            print(f"   {member.name} = \"{member.value}\"")
        
        print(f"\n📝 Usage examples:")
        print(f"   EntityType.PERSON.value  # Returns: \"{EntityType.PERSON.value if hasattr(EntityType, 'PERSON') else 'Person'}\"")
        print(f"   RelationshipType.WORKS_AT.value  # Returns: \"{RelationshipType.WORKS_AT.value if hasattr(RelationshipType, 'WORKS_AT') else 'works_at'}\"")
        
    except Exception as e:
        print(f"❌ Error generating Pydantic enums: {e}")


def pretty_print_function_schema(ontology: dict):
    """Pretty print the generated function calling schema"""
    print("\n" + "=" * 80)
    print("4️⃣ GENERATED FUNCTION SCHEMA (for LLM function calling)")
    print("=" * 80)
    
    try:
        function_schema = generate_function_schema(ontology)
        
        # Extract key information
        function_name = function_schema["function"]["name"]
        entity_enum = function_schema["function"]["parameters"]["properties"]["triples"]["items"]["properties"]["entity_subject_type"]["enum"]
        relationship_enum = function_schema["function"]["parameters"]["properties"]["triples"]["items"]["properties"]["relationship_type"]["enum"]
        
        print(f"🔧 Function name: {function_name}")
        print(f"🏷️  Entity enum: {entity_enum}")
        print(f"🔗 Relationship enum: {relationship_enum}")
        print(f"📏 Schema size: {len(json.dumps(function_schema))} characters")
        print()
        print("📋 Full JSON Schema:")
        print(json.dumps(function_schema, indent=2))
        
    except Exception as e:
        print(f"❌ Error generating function schema: {e}")


def test_ontology_generation(cypher_file_path: str = None):
    """
    Test ontology generation from Cypher file to all 3 formats
    
    Args:
        cypher_file_path: Path to Cypher file (defaults to test ontology)
    """
    # Use default test ontology if none provided
    if cypher_file_path is None:
        cypher_file_path = "tests/fixtures/test_ontology.cypher"
    
    print("🧪 Ontology Generation Test")
    print(f"📁 Using ontology file: {cypher_file_path}")
    
    # Check if file exists
    if not Path(cypher_file_path).exists():
        print(f"❌ Ontology file not found: {cypher_file_path}")
        print("💡 Available ontology files:")
        
        # Show available ontology files
        for pattern in ["examples/ontologies/*.cypher", "tests/fixtures/*.cypher"]:
            for file_path in Path(".").glob(pattern):
                print(f"   - {file_path}")
        return
    
    # 1. Show original Cypher content
    if not pretty_print_cypher(cypher_file_path):
        return
    
    # 2. Load and parse ontology
    try:
        print(f"\n🔄 Loading ontology from {cypher_file_path}...")
        ontology = load_ontology_from_cypher(cypher_file_path)
        print(f"✅ Successfully loaded ontology")
        
    except Exception as e:
        print(f"❌ Failed to load ontology: {e}")
        return
    
    # 3. Generate and display all 3 formats
    pretty_print_prompt_ontology(ontology)
    pretty_print_pydantic_enums(ontology)
    pretty_print_function_schema(ontology)
    
    # 4. Summary
    print("\n" + "=" * 80)
    print("✅ ONTOLOGY GENERATION TEST COMPLETE")
    print("=" * 80)
    print(f"📁 Source: {cypher_file_path}")
    print(f"🏷️  Entities: {len(ontology['entities'])}")
    print(f"🔗 Relationships: {len(ontology['relationships'])}")
    print("🎯 All 4 formats generated successfully!")


if __name__ == "__main__":
    # Parse command line arguments
    if len(sys.argv) > 1:
        cypher_file_path = sys.argv[1]
        test_ontology_generation(cypher_file_path)
    else:
        test_ontology_generation()  # Use default test ontology
