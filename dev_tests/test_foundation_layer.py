"""
Development Test Script for Foundation Layer Functions
These are temporary tests for development purposes, separate from production tests
"""

import sys
sys.path.append("src")

from core.pipeline import row_to_json, extract_triples
from config.llm_config import LLM_CONFIGS, get_llm_config
from ontology.loader import load_ontology_from_cypher

def test_row_to_json():
    """Test row_to_json function"""
    print("Testing row_to_json()...")

    # Test case 1: Basic functionality
    sample_row = {
        'UID': 'doc_001',
        'freeform_text': '<PERSON> works at TechCorp as VP of Engineering.',
        'source': 'company_directory',
        'category': 'employee_info'
    }

    result = row_to_json(sample_row)
    
    assert result['document_id'] == 'doc_001'
    assert result['freeform_text'] == '<PERSON> works at TechCorp as VP of Engineering.'
    assert result['metadata']['source'] == 'company_directory'
    assert result['metadata']['category'] == 'employee_info'
    assert 'processing_timestamp' in result
    
    print("✅ Basic functionality test passed")
    
    # Test case 2: Error handling
    try:
        row_to_json({'freeform_text': 'text without UID'})
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "UID" in str(e)
        print("✅ Error handling test passed")

    print("✅ row_to_json() tests completed successfully\n")


def test_llm_config():
    """Test LLM configuration imports and functions"""
    print("Testing LLM configuration...")

    # Test case 1: Static config access - OpenAI
    config = LLM_CONFIGS["openai"]
    assert config["provider"] == "openai"
    assert config["model"] == "gpt-4o-mini"
    assert config["temperature"] == 0.1
    assert config["max_tokens"] == 4000
    assert config["api_key_env"] == "OPENAI_API_KEY"
    print("✅ OpenAI static config test passed")

    # Test case 2: Static config access - Anthropic
    config = LLM_CONFIGS["anthropic"]
    assert config["provider"] == "anthropic"
    assert config["model"] == "claude-3-5-sonnet-20241022"
    assert config["temperature"] == 0.1
    assert config["max_tokens"] == 4000
    assert config["api_key_env"] == "ANTHROPIC_API_KEY"
    print("✅ Anthropic static config test passed")

    # Test case 2b: Static config access - Ollama
    config = LLM_CONFIGS["ollama"]
    assert config["provider"] == "ollama"
    # Model now comes from env var, so just check it exists
    assert "model" in config
    assert config["temperature"] == 0.1  # Default if no env var
    assert config["max_tokens"] == 4000   # Default if no env var
    assert config["api_key_env"] is None  # Ollama doesn't need API key
    assert "base_url" in config
    print("✅ Ollama static config test passed")

    # Test case 3: Error handling - invalid provider in static config
    try:
        config = LLM_CONFIGS["invalid_provider"]
        assert False, "Should have raised KeyError"
    except KeyError:
        print("✅ Static config error handling test passed")

    # Test case 4: get_llm_config with invalid provider
    try:
        get_llm_config("invalid_provider")
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "Unsupported provider" in str(e)
        print("✅ get_llm_config error handling test passed")

    # Note: Testing get_llm_config with valid providers requires API keys
    # This would fail in CI/testing without env vars, which is correct behavior
    print("✅ LLM configuration tests completed successfully\n")


def test_extract_triples():
    """Test extract_triples function (requires LLM provider)"""
    print("Testing extract_triples()...")

    # Load test ontology
    try:
        ontology = load_ontology_from_cypher("examples/ontologies/default_ontology.cypher")
    except Exception as e:
        print(f"⚠️  Could not load ontology: {e}")
        print("✅ extract_triples() tests completed successfully (ontology loading skipped)")
        return

    # Test case 1: Input validation
    try:
        extract_triples("not a dict", {}, ontology)
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "text_json must be a dictionary" in str(e)
        print("✅ Input validation test passed")

    # Test case 2: Missing required fields
    try:
        extract_triples({"missing": "fields"}, {"provider": "openai", "model": "gpt-4o-mini"}, ontology)
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "missing required field" in str(e)
        print("✅ Required fields validation test passed")

    # Test case 3: Real extraction (only if LLM provider available)
    try:
        # Create sample input
        text_json = {
            "document_id": "test_doc_001",
            "freeform_text": "Sarah Johnson works at TechCorp as VP of Engineering.",
            "metadata": {"source": "test"},
            "processing_timestamp": "2024-01-15T10:30:00"
        }

        # Try to get LLM config (will fail if no provider available)
        llm_config = get_llm_config()

        # Perform extraction
        result = extract_triples(text_json, llm_config, ontology)

        # Validate result structure (now it's just an array)
        assert isinstance(result, list)

        print(f"✅ Real extraction test passed - extracted {len(result)} triples")

        # Show sample triple if available
        if result:
            triple = result[0]
            # Validate triple structure
            assert "document_id" in triple
            assert "entity_subject" in triple
            assert "relationship_type" in triple
            assert "entity_object" in triple
            assert "extraction_timestamp" in triple
            assert triple["document_id"] == "test_doc_001"
            print(f"   Sample: {triple['entity_subject']} --[{triple['relationship_type']}]--> {triple['entity_object']}")

    except (EnvironmentError, RuntimeError) as e:
        print(f"⚠️  Real extraction test skipped: {e}")
        print("   (This is expected if no LLM provider is configured)")

    print("✅ extract_triples() tests completed successfully\n")


if __name__ == "__main__":
    test_row_to_json()
    test_llm_config()
    test_extract_triples()