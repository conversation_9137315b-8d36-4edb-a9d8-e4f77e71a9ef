# Version 1: LLM + Function Calling

This folder contains the implementation of Version 1 of the graph ingestion system, which uses LLM function calling to extract entities and relationships from freeform text.

## Files

- **`graph_ingestion_v1.py`**: Main Marimo notebook with interactive interface
- **`test_extraction.py`**: Test script to verify all functionality
- **`README.md`**: This file

## Quick Start

### 1. Install Dependencies

```bash
cd /Users/<USER>/Repos/graph_ingestion
uv sync
```

### 2. Set Up Environment

Copy the example environment file and add your API keys:

```bash
cp .env.example .env
# Edit .env with your API keys
```

### 3. Start Memgraph

```bash
docker run -p 7687:7687 memgraph/memgraph
```

### 4. Run Tests

```bash
cd notebooks/v1_llm_function_calling
python test_extraction.py
```

### 5. Launch Interactive Notebook

```bash
cd notebooks/v1_llm_function_calling
marimo edit graph_ingestion_v1.py
```

## Features

### ✅ Core Functionality
- LLM-based entity and relationship extraction
- Support for OpenAI and Anthropic APIs
- Structured output with 14-column tabular schema
- Memgraph integration for graph storage
- Quality metrics and validation
- CSV/JSON export capabilities

### ✅ Interactive Interface
- Marimo notebook with web-based UI
- Configuration management
- Real-time extraction and visualization
- Quality metrics dashboard
- Graph statistics and exploration

### ✅ Data Pipeline
- Text input → LLM function calling → Pydantic validation
- Tabular schema generation with full metadata
- Parallel storage in Memgraph and local files
- Quality assurance and error handling

## Architecture

```
Text Input → LLM Function Calling → Pydantic Validation →
Tabular Schema Generation → Parallel Storage: [Memgraph + CSV/JSON] →
Quality Metrics Dashboard
```

## Performance Targets

- **Text Complexity**: Simple paragraphs (<1000 words)
- **Processing Speed**: 30-60 seconds per text passage
- **Expected Accuracy**: 75-85% entities, 70-80% relationships
- **Cost**: High ($0.50-1.00 per 1K words)
- **Success Rate**: 90% of extractions without manual intervention

## Sample Data

The system includes sample texts from the README that demonstrate:
- Person-organization relationships
- Role assignments
- Project management
- Location associations
- Product development
- Event participation

## Ontology Support

### Entity Types (7)
- Person, Organization, Location, Project, Event, Product, Role

### Relationship Types (9)
- works_for, located_at, manages, participates_in, reports_to, collaborates_with, occurred_at, develops, holds_role

## Quality Metrics

The system tracks:
- **Extraction Success Rate**: Percentage of successful extractions
- **Confidence Scores**: Average confidence across all triples
- **Entity Coverage**: Distribution of entity types found
- **Relationship Coverage**: Distribution of relationship types found
- **Validation Issues**: Data quality problems detected

## Export Formats

- **CSV**: Tabular format with all 14 columns for spreadsheet analysis
- **JSON**: Structured format with full metadata preservation
- **Memgraph**: Live graph database for querying and visualization

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Ensure OPENAI_API_KEY or ANTHROPIC_API_KEY is set
   - Check API key permissions and billing

2. **Memgraph Connection Errors**
   - Verify Memgraph is running: `docker ps`
   - Check port 7687 is available
   - Restart Memgraph if needed

3. **Import Errors**
   - Ensure you're in the correct directory
   - Check Python path includes src/ directory
   - Verify all dependencies are installed

4. **Low Extraction Quality**
   - Check input text quality and length
   - Ensure ontology matches your domain
   - Consider using different LLM models

### Performance Tips

- Use shorter, well-structured text for better accuracy
- Ensure consistent entity naming in your text
- Review and refine the ontology for your domain
- Monitor confidence scores to identify problematic extractions

## Next Steps

After successfully running Version 1:

1. **Experiment with Different Texts**: Try your own domain-specific content
2. **Customize the Ontology**: Modify entity/relationship types for your use case
3. **Analyze Results**: Use the quality metrics to improve extraction accuracy
4. **Scale Up**: Consider moving to Version 2 for larger documents
5. **Integrate**: Connect the graph to your downstream applications

## Support

For issues or questions:
1. Check the test script output for specific error messages
2. Review the Marimo notebook logs for debugging info
3. Consult the main README for architecture details
4. Open an issue in the repository with your error details