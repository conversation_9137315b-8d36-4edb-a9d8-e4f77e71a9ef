"""
Test script for Version 1 extraction functionality
Run this to verify the implementation works correctly
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from extraction.llm_extractor import LLMExtractor
from validation.quality_metrics import QualityMetrics
from graph_construction.memgraph_client import MemgraphClient
from ontology.models import ExtractionResult

def test_extraction():
    """Test LLM extraction functionality"""
    print("🧪 Testing LLM Extraction...")
    
    # Sample text
    text = """<PERSON>, the VP of Engineering at TechCorp, is leading the AI initiative project 
    in collaboration with DataSoft. The project kicked off last month at the San Francisco headquarters."""
    
    document_id = "TEST_001"
    
    # Test with OpenAI (if API key is available)
    if os.getenv("OPENAI_API_KEY"):
        print("Testing OpenAI extraction...")
        try:
            extractor = LLMExtractor(provider="openai")
            result = extractor.extract(text, document_id)
            
            print(f"✅ OpenAI extraction successful!")
            print(f"   - Document ID: {result.document_id}")
            print(f"   - Triples extracted: {len(result.triples)}")
            
            for i, triple in enumerate(result.triples):
                print(f"   - Triple {i+1}: {triple.entity_subject} --[{triple.relationship_type}]--> {triple.entity_object}")
            
            return result
            
        except Exception as e:
            print(f"❌ OpenAI extraction failed: {str(e)}")
    
    # Test with Anthropic (if API key is available)
    elif os.getenv("ANTHROPIC_API_KEY"):
        print("Testing Anthropic extraction...")
        try:
            extractor = LLMExtractor(provider="anthropic")
            result = extractor.extract(text, document_id)
            
            print(f"✅ Anthropic extraction successful!")
            print(f"   - Document ID: {result.document_id}")
            print(f"   - Triples extracted: {len(result.triples)}")
            
            for i, triple in enumerate(result.triples):
                print(f"   - Triple {i+1}: {triple.entity_subject} --[{triple.relationship_type}]--> {triple.entity_object}")
            
            return result
            
        except Exception as e:
            print(f"❌ Anthropic extraction failed: {str(e)}")
    
    else:
        print("❌ No API keys found. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY")
        return None

def test_quality_metrics(result: ExtractionResult):
    """Test quality metrics functionality"""
    print("\n📊 Testing Quality Metrics...")
    
    try:
        quality_calc = QualityMetrics()
        
        # Calculate basic metrics
        metrics = quality_calc.calculate_basic_metrics(result)
        print(f"✅ Basic metrics calculated!")
        print(f"   - Total triples: {metrics['total_triples']}")
        print(f"   - Average confidence: {metrics['avg_confidence']:.2f}")
        print(f"   - Entity types: {list(metrics['entity_types_coverage'].keys())}")
        print(f"   - Relationship types: {list(metrics['relationship_types_coverage'].keys())}")
        
        # Calculate entity metrics
        entity_metrics = quality_calc.calculate_entity_metrics(result)
        print(f"✅ Entity metrics calculated!")
        print(f"   - Unique entities: {entity_metrics['unique_entities']}")
        print(f"   - Most connected: {entity_metrics['most_connected_entities'][:3]}")
        
        # Validate result
        validation = quality_calc.validate_extraction_result(result)
        print(f"✅ Validation completed!")
        print(f"   - Is valid: {validation['is_valid']}")
        print(f"   - Issues: {validation['total_issues']}")
        print(f"   - Warnings: {validation['total_warnings']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quality metrics failed: {str(e)}")
        return False

def test_memgraph_connection():
    """Test Memgraph connection"""
    print("\n🔗 Testing Memgraph Connection...")
    
    try:
        with MemgraphClient() as client:
            # Test basic query
            result = client.execute_query("RETURN 1 as test")
            print(f"✅ Memgraph connection successful!")
            print(f"   - Query result: {result}")
            
            # Test schema setup
            client.setup_schema()
            print(f"✅ Schema setup completed!")
            
            # Get stats
            stats = client.get_graph_stats()
            print(f"✅ Graph stats retrieved!")
            print(f"   - Total entities: {stats.get('total_entities', 'N/A')}")
            print(f"   - Total relationships: {stats.get('total_relationships', 'N/A')}")
            
            return True
            
    except Exception as e:
        print(f"❌ Memgraph connection failed: {str(e)}")
        print("   Make sure Memgraph is running: docker run -p 7687:7687 memgraph/memgraph")
        return False

def test_graph_insertion(result: ExtractionResult):
    """Test graph insertion functionality"""
    print("\n📈 Testing Graph Insertion...")
    
    try:
        with MemgraphClient() as client:
            # Clear existing data
            client.clear_graph()
            print("✅ Graph cleared")
            
            # Insert extraction result
            success = client.insert_extraction_result(result)
            
            if success:
                print(f"✅ Graph insertion successful!")
                
                # Get updated stats
                stats = client.get_graph_stats()
                print(f"   - Entities in graph: {stats.get('total_entities', 'N/A')}")
                print(f"   - Relationships in graph: {stats.get('total_relationships', 'N/A')}")
                
                # Test a simple query
                entities = client.execute_query("MATCH (e:Entity) RETURN e.name as name, e.type as type LIMIT 5")
                print(f"   - Sample entities: {[f\"{e['name']} ({e['type']})\" for e in entities]}")
                
                return True
            else:
                print("❌ Graph insertion failed")
                return False
                
    except Exception as e:
        print(f"❌ Graph insertion failed: {str(e)}")
        return False

def test_export_functionality(result: ExtractionResult):
    """Test export functionality"""
    print("\n💾 Testing Export Functionality...")
    
    try:
        quality_calc = QualityMetrics()
        
        # Test CSV export
        csv_file = quality_calc.export_to_csv([result])
        print(f"✅ CSV export successful!")
        print(f"   - File: {csv_file}")
        
        # Test JSON export
        json_file = quality_calc.export_to_json([result])
        print(f"✅ JSON export successful!")
        print(f"   - File: {json_file}")
        
        # Check if files exist
        csv_exists = os.path.exists(csv_file)
        json_exists = os.path.exists(json_file)
        
        print(f"   - CSV file exists: {csv_exists}")
        print(f"   - JSON file exists: {json_exists}")
        
        return csv_exists and json_exists
        
    except Exception as e:
        print(f"❌ Export functionality failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Version 1 Tests...")
    print("=" * 50)
    
    # Test 1: Extraction
    result = test_extraction()
    if not result:
        print("❌ Extraction test failed - stopping here")
        return
    
    # Test 2: Quality Metrics
    quality_success = test_quality_metrics(result)
    
    # Test 3: Memgraph Connection
    memgraph_success = test_memgraph_connection()
    
    # Test 4: Graph Insertion (only if connection works)
    if memgraph_success:
        insertion_success = test_graph_insertion(result)
    else:
        insertion_success = False
    
    # Test 5: Export Functionality
    export_success = test_export_functionality(result)
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print(f"   - Extraction: {'✅' if result else '❌'}")
    print(f"   - Quality Metrics: {'✅' if quality_success else '❌'}")
    print(f"   - Memgraph Connection: {'✅' if memgraph_success else '❌'}")
    print(f"   - Graph Insertion: {'✅' if insertion_success else '❌'}")
    print(f"   - Export: {'✅' if export_success else '❌'}")
    
    total_tests = 5
    passed_tests = sum([
        bool(result), 
        quality_success, 
        memgraph_success, 
        insertion_success, 
        export_success
    ])
    
    print(f"\n🏆 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Version 1 is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()