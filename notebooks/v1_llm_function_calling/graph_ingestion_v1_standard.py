#!/usr/bin/env python3
"""
Version 1 - LLM + Function Calling: End-to-End Demonstration
============================================================

This script demonstrates the complete Version 1 pipeline from freeform text to knowledge graph.
Follow along with the extensive comments to understand each step of the process.

Architecture Overview:
    Text Input → LLM Function Calling → Pydantic Validation →
    Tabular Schema Generation → Parallel Storage: [Memgraph + CSV/JSON] →
    Quality Metrics Dashboard

Author: Claude Code
Date: 2025-07-09
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# TODO - remove this and find better way
# Add src to path for imports
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
src_path = project_root / "src"
sys.path.append(str(src_path))

# Import our custom modules
from ontology.models import (
    EntityType,
    RelationshipType,
    EntityRelationshipTriple,
    ExtractionResult
)
from extraction.llm_extractor import LLMExtractor
from validation.quality_metrics import QualityMetrics
from utils.output_manager import OutputManager

# Try to import Memgraph client, but handle gracefully if not available
try:
    from graph_construction.memgraph_client import MemgraphClient
    MEMGRAPH_AVAILABLE = True
except ImportError as e:
    print(f"Note: Memgraph client not available: {e}")
    MEMGRAPH_AVAILABLE = False
    MemgraphClient = None


def print_section(title: str, description: str = ""):
    """Helper function to print section headers"""
    print("\n" + "=" * 80)
    print(f"🔹 {title}")
    if description:
        print(f"   {description}")
    print("=" * 80)


def print_subsection(title: str):
    """Helper function to print subsection headers"""
    print(f"\n📋 {title}")
    print("-" * 50)


def load_sample_data() -> List[Dict[str, str]]:
    """
    Step 1: Load Sample Data
    ========================

    Load sample texts from our data directory. These represent the kind of
    freeform text inputs that users would provide to the system.

    Returns:
        List of dictionaries with 'document_id' and 'text' keys
    """
    print_section("STEP 1: Load Sample Data",
                  "Loading freeform text examples for processing")

    # Path to our sample data
    sample_file = project_root / "data" / "samples" / "sample_texts.json"

    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            sample_data = json.load(f)

        print(f"✅ Loaded {len(sample_data)} sample texts from {sample_file}")

        # Display the samples
        for i, sample in enumerate(sample_data, 1):
            print(f"\n   Sample {i} ({sample['document_id']}):")
            print(f"   {sample['text'][:100]}...")

        return sample_data

    except FileNotFoundError:
        print(f"❌ Sample file not found: {sample_file}")
        print("   Creating a basic example...")

        # Fallback sample data
        return [{
            "document_id": "DEMO_001",
            "text": "Sarah Johnson, the VP of Engineering at TechCorp, is leading the AI initiative project in collaboration with DataSoft. The project kicked off last month at the San Francisco headquarters."
        }]


def demonstrate_ontology():
    """
    Step 2: Demonstrate Ontology System
    ====================================

    Show the predefined ontology that guides our extraction process.
    This ontology defines what types of entities and relationships
    we can extract from text.
    """
    print_section("STEP 2: Ontology System",
                  "Understanding the predefined entity and relationship types")

    print_subsection("Entity Types (7 types)")
    print("Our system can identify these types of entities:")
    for entity_type in EntityType:
        print(f"   • {entity_type.value}")

    print_subsection("Relationship Types (9 types)")
    print("Our system can identify these types of relationships:")
    for rel_type in RelationshipType:
        print(f"   • {rel_type.value}")

    print_subsection("14-Column Tabular Schema")
    print("Each extracted relationship becomes a row with these columns:")
    schema_columns = [
        "entity_subject", "relationship_type", "entity_object",
        "entity_subject_type", "entity_object_type", "confidence_score",
        "source_text_snippet", "extraction_method", "document_id",
        "chunk_id", "relationship_context", "extraction_timestamp",
        "entity_subject_id", "entity_object_id"
    ]
    for i, col in enumerate(schema_columns, 1):
        print(f"   {i:2d}. {col}")


def setup_llm_extractor() -> Optional[LLMExtractor]:
    """
    Step 3: Setup LLM Extractor
    ============================

    Initialize the LLM extractor that will perform the actual entity and
    relationship extraction. This component uses function calling to get
    structured output from OpenAI or Anthropic APIs.

    Returns:
        LLMExtractor instance if API keys are available, None otherwise
    """
    print_section("STEP 3: Setup LLM Extractor",
                  "Initializing the core extraction component")

    # TODO change to use dotenv
    # Check for API keys
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")

    print_subsection("API Key Status")
    print(f"   OpenAI API Key: {'✅ Available' if openai_key else '❌ Not set'}")
    print(f"   Anthropic API Key: {'✅ Available' if anthropic_key else '❌ Not set'}")

    if openai_key:
        print("\n🤖 Using OpenAI GPT-4o-mini for extraction")
        extractor = LLMExtractor(provider="openai", model="gpt-4o-mini")
        print("   ✅ OpenAI extractor initialized successfully")
        return extractor

    elif anthropic_key:
        print("\n🤖 Using Anthropic Claude-3.5-Sonnet for extraction")
        extractor = LLMExtractor(provider="anthropic", model="claude-3-5-sonnet-20241022")
        print("   ✅ Anthropic extractor initialized successfully")
        return extractor

    else:
        print("\n❌ No API keys found!")
        print("   To enable LLM extraction:")
        print("   1. Copy .env.example to .env")
        print("   2. Add your OPENAI_API_KEY or ANTHROPIC_API_KEY")
        print("   3. Re-run this script")
        print("\n   Continuing with simulated extraction results...")
        return None


def perform_extraction(extractor: Optional[LLMExtractor],
                      sample_data: List[Dict[str, str]]) -> List[ExtractionResult]:
    """
    Step 4: Perform Extraction
    ===========================

    Extract entities and relationships from each sample text using the LLM.
    This is the core of our pipeline where unstructured text becomes
    structured entity-relationship triples.

    Args:
        extractor: LLMExtractor instance (or None for simulation)
        sample_data: List of text samples to process

    Returns:
        List of ExtractionResult objects containing the extracted triples
    """
    print_section("STEP 4: LLM Extraction Process",
                  "Converting freeform text into structured entity-relationship triples")

    extraction_results = []

    for i, sample in enumerate(sample_data, 1):
        print_subsection(f"Processing Sample {i}: {sample['document_id']}")
        print(f"Text: {sample['text'][:100]}...")

        if extractor:
            try:
                # Real LLM extraction
                print("   🤖 Calling LLM for extraction...")
                result = extractor.extract(sample['text'], sample['document_id'])

                print(f"   ✅ Extraction successful! Found {len(result.triples)} triples")

                # Display extracted triples
                for j, triple in enumerate(result.triples, 1):
                    print(f"      {j}. {triple.entity_subject} --[{triple.relationship_type}]--> {triple.entity_object}")
                    print(f"         Types: {triple.entity_subject_type} → {triple.entity_object_type}")
                    print(f"         Confidence: {triple.confidence_score:.2f}")
                    print(f"         Source: \"{triple.source_text_snippet[:50]}...\"")

                extraction_results.append(result)

            except Exception as e:
                print(f"   ❌ Extraction failed: {str(e)}")
                # Continue with next sample

        else:
            # Simulated extraction for demo purposes
            print("   🎭 Simulating extraction (no API key available)...")

            # Create realistic simulated triples based on the sample text
            simulated_triples = create_simulated_triples(sample)

            result = ExtractionResult(
                triples=simulated_triples,
                document_id=sample['document_id'],
                total_triples=len(simulated_triples)
            )

            print(f"   ✅ Simulation complete! Generated {len(result.triples)} triples")

            # Display simulated triples
            for j, triple in enumerate(result.triples, 1):
                print(f"      {j}. {triple.entity_subject} --[{triple.relationship_type}]--> {triple.entity_object}")
                print(f"         Types: {triple.entity_subject_type} → {triple.entity_object_type}")
                print(f"         Confidence: {triple.confidence_score:.2f}")

            extraction_results.append(result)

    print(f"\n🎯 Total extraction results: {len(extraction_results)}")
    return extraction_results


def create_simulated_triples(sample: Dict[str, str]) -> List[EntityRelationshipTriple]:
    """
    Create realistic simulated triples for demonstration when no API key is available.

    Args:
        sample: Sample data dictionary

    Returns:
        List of EntityRelationshipTriple objects
    """
    import uuid

    # Different simulated triples based on document ID
    if "UID_1" in sample['document_id'] or "DEMO" in sample['document_id']:
        return [
            EntityRelationshipTriple(
                entity_subject="Sarah Johnson",
                relationship_type=RelationshipType.HOLDS_ROLE,
                entity_object="VP of Engineering",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.ROLE,
                source_text_snippet="Sarah Johnson, the VP of Engineering",
                relationship_context="Role assignment",
                document_id=sample['document_id'],
                entity_subject_id=f"sarah_johnson_{uuid.uuid4().hex[:8]}",
                entity_object_id=f"vp_engineering_{uuid.uuid4().hex[:8]}"
            ),
            EntityRelationshipTriple(
                entity_subject="Sarah Johnson",
                relationship_type=RelationshipType.WORKS_FOR,
                entity_object="TechCorp",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.ORGANIZATION,
                source_text_snippet="VP of Engineering at TechCorp",
                relationship_context="Employment relationship",
                document_id=sample['document_id'],
                entity_subject_id=f"sarah_johnson_{uuid.uuid4().hex[:8]}",
                entity_object_id=f"techcorp_{uuid.uuid4().hex[:8]}"
            ),
            EntityRelationshipTriple(
                entity_subject="TechCorp",
                relationship_type=RelationshipType.COLLABORATES_WITH,
                entity_object="DataSoft",
                entity_subject_type=EntityType.ORGANIZATION,
                entity_object_type=EntityType.ORGANIZATION,
                source_text_snippet="in collaboration with DataSoft",
                relationship_context="Business partnership",
                document_id=sample['document_id'],
                entity_subject_id=f"techcorp_{uuid.uuid4().hex[:8]}",
                entity_object_id=f"datasoft_{uuid.uuid4().hex[:8]}"
            )
        ]

    elif "UID_2" in sample['document_id']:
        return [
            EntityRelationshipTriple(
                entity_subject="Mike Chen",
                relationship_type=RelationshipType.DEVELOPS,
                entity_object="customer analytics platform",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.PRODUCT,
                source_text_snippet="Mike Chen presented the new customer analytics platform",
                relationship_context="Product development",
                document_id=sample['document_id'],
                entity_subject_id=f"mike_chen_{uuid.uuid4().hex[:8]}",
                entity_object_id=f"analytics_platform_{uuid.uuid4().hex[:8]}"
            )
        ]

    else:
        # Generic simulation for other samples
        return [
            EntityRelationshipTriple(
                entity_subject="Sample Entity",
                relationship_type=RelationshipType.WORKS_FOR,
                entity_object="Sample Organization",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.ORGANIZATION,
                source_text_snippet="Sample text snippet",
                relationship_context="Sample relationship",
                document_id=sample['document_id'],
                entity_subject_id=f"sample_entity_{uuid.uuid4().hex[:8]}",
                entity_object_id=f"sample_org_{uuid.uuid4().hex[:8]}"
            )
        ]


def analyze_quality_metrics(extraction_results: List[ExtractionResult]) -> Dict[str, Any]:
    """
    Step 5: Quality Metrics Analysis
    =================================

    Analyze the quality of our extractions using various metrics including
    confidence scores, entity/relationship distributions, and validation checks.

    Args:
        extraction_results: List of extraction results to analyze

    Returns:
        Dictionary containing comprehensive quality metrics
    """
    print_section("STEP 5: Quality Metrics Analysis",
                  "Analyzing extraction quality and generating validation reports")

    # Initialize quality metrics calculator
    quality_calc = QualityMetrics()

    print_subsection("Individual Document Metrics")
    for i, result in enumerate(extraction_results, 1):
        print(f"\n   Document {i}: {result.document_id}")

        # Basic metrics for this document
        basic_metrics = quality_calc.calculate_basic_metrics(result)
        print(f"      Triples extracted: {basic_metrics['total_triples']}")
        print(f"      Average confidence: {basic_metrics['avg_confidence']:.2f}")
        print(f"      Entity types: {list(basic_metrics['entity_types_coverage'].keys())}")
        print(f"      Relationship types: {list(basic_metrics['relationship_types_coverage'].keys())}")

        # Entity metrics
        entity_metrics = quality_calc.calculate_entity_metrics(result)
        print(f"      Unique entities: {entity_metrics['unique_entities']}")

        # Validation
        validation = quality_calc.validate_extraction_result(result)
        status = "✅ Valid" if validation['is_valid'] else "❌ Issues found"
        print(f"      Validation: {status}")
        if validation['issues']:
            for issue in validation['issues']:
                print(f"         Issue: {issue}")
        if validation['warnings']:
            for warning in validation['warnings']:
                print(f"         Warning: {warning}")

    print_subsection("Comprehensive Quality Report")

    # Generate comprehensive report across all documents
    quality_report = quality_calc.generate_quality_report(extraction_results)

    print("   📊 Summary Statistics:")
    summary = quality_report['summary']
    print(f"      Total documents processed: {summary['total_documents']}")
    print(f"      Total triples extracted: {summary['total_triples']}")
    print(f"      Average triples per document: {summary['avg_triples_per_doc']:.1f}")
    print(f"      Average confidence score: {summary['avg_confidence']:.2f}")
    print(f"      Success rate: {summary['success_rate']:.1%}")

    print("\n   📈 Entity Type Distribution:")
    for entity_type, count in quality_report['distributions']['entity_types'].items():
        print(f"      {entity_type}: {count}")

    print("\n   📈 Relationship Type Distribution:")
    for rel_type, count in quality_report['distributions']['relationship_types'].items():
        print(f"      {rel_type}: {count}")

    return quality_report


def export_results(extraction_results: List[ExtractionResult],
                  quality_report: Dict[str, Any],
                  output_manager: OutputManager) -> Dict[str, Path]:
    """
    Step 6: Export Results
    ======================

    Export extraction results to various formats for analysis, storage,
    and integration with other systems using organized directory structure.

    Args:
        extraction_results: List of extraction results
        quality_report: Quality metrics report
        output_manager: OutputManager for organized file handling

    Returns:
        Dictionary mapping format names to file paths
    """
    print_section("STEP 6: Export Results",
                  "Saving extraction results and quality metrics to organized directories")

    exported_files = {}

    print_subsection("Organized Directory Structure")
    session_info = output_manager.get_session_info()
    print(f"   📁 Session ID: {session_info['session_id']}")
    print(f"   📁 Outputs directory: {session_info['outputs_dir']}")
    print(f"   📁 Session directory: {session_info['session_dir']}")

    print_subsection("Save Extraction Results")
    try:
        extraction_path = output_manager.save_extraction_results(extraction_results, version="v1")
        exported_files['extraction_results'] = extraction_path

        file_size = extraction_path.stat().st_size
        print(f"   ✅ Extraction results saved: {extraction_path}")
        print(f"      File size: {file_size} bytes")
        print(f"      Contains: Complete extraction metadata and {sum(len(r.triples) for r in extraction_results)} triples")

    except Exception as e:
        print(f"   ❌ Extraction results save failed: {str(e)}")

    print_subsection("Save Quality Report")
    try:
        quality_path = output_manager.save_quality_report(quality_report, version="v1")
        exported_files['quality_report'] = quality_path

        file_size = quality_path.stat().st_size
        print(f"   ✅ Quality report saved: {quality_path}")
        print(f"      File size: {file_size} bytes")
        print(f"      Contains: Comprehensive quality metrics and validation results")
        print(f"      Also saved to session directory for tracking")

    except Exception as e:
        print(f"   ❌ Quality report save failed: {str(e)}")

    print_subsection("CSV Export (Tabular Format)")
    try:
        csv_path = output_manager.export_csv(extraction_results, custom_name="v1_extraction_results")
        exported_files['csv'] = csv_path

        file_size = csv_path.stat().st_size
        print(f"   ✅ CSV exported: {csv_path}")
        print(f"      File size: {file_size} bytes")
        print(f"      Contains: All {sum(len(r.triples) for r in extraction_results)} triples with 14 columns")

    except Exception as e:
        print(f"   ❌ CSV export failed: {str(e)}")

    print_subsection("JSON Export (Structured Format)")
    try:
        json_path = output_manager.export_json(extraction_results, custom_name="v1_extraction_results")
        exported_files['json'] = json_path

        file_size = json_path.stat().st_size
        print(f"   ✅ JSON exported: {json_path}")
        print(f"      File size: {file_size} bytes")
        print(f"      Contains: Full metadata and hierarchical structure")

    except Exception as e:
        print(f"   ❌ JSON export failed: {str(e)}")

    print_subsection("Session Summary")
    try:
        summary_path = output_manager.create_session_summary(extraction_results, quality_report, exported_files)
        exported_files['session_summary'] = summary_path

        file_size = summary_path.stat().st_size
        print(f"   ✅ Session summary created: {summary_path}")
        print(f"      File size: {file_size} bytes")
        print(f"      Contains: Complete session overview and next steps")

    except Exception as e:
        print(f"   ❌ Session summary creation failed: {str(e)}")

    return exported_files


def test_memgraph_integration(extraction_results: List[ExtractionResult]) -> bool:
    """
    Step 7: Memgraph Integration (Optional)
    =======================================

    Demonstrate integration with Memgraph knowledge graph database.
    This step is optional and requires a running Memgraph instance.

    Args:
        extraction_results: List of extraction results to store in graph

    Returns:
        True if successful, False otherwise
    """
    print_section("STEP 7: Memgraph Integration (Optional)",
                  "Storing extraction results in knowledge graph database")

    print_subsection("Connection Test")
    if not MEMGRAPH_AVAILABLE:
        print("   ❌ Memgraph client not available")
        print("      To install: brew install cmake && uv run pip install pymgclient")
        return False

    try:
        # Test if pymgclient is available
        import pymgclient
        print("   ✅ pymgclient module available")
    except ImportError:
        print("   ❌ pymgclient not installed")
        print("      To install: brew install cmake && uv run pip install pymgclient")
        return False

    try:
        # Test Memgraph connection
        with MemgraphClient() as client:
            # Test basic connectivity
            result = client.execute_query("RETURN 1 as test")
            print(f"   ✅ Memgraph connection successful: {result}")

            print_subsection("Schema Setup")
            client.setup_schema()
            print("   ✅ Graph schema initialized with indexes")

            print_subsection("Data Insertion")
            total_inserted = 0
            for i, extraction_result in enumerate(extraction_results, 1):
                print(f"\n   Inserting result {i}: {extraction_result.document_id}")

                success = client.insert_extraction_result(extraction_result)
                if success:
                    print(f"      ✅ Inserted {len(extraction_result.triples)} triples")
                    total_inserted += len(extraction_result.triples)
                else:
                    print(f"      ❌ Insertion failed")

            print_subsection("Graph Statistics")
            stats = client.get_graph_stats()

            print("   📊 Current graph state:")
            if 'total_entities' in stats and stats['total_entities']:
                for stat in stats['total_entities']:
                    print(f"      Total entities: {stat.get('count', 'N/A')}")

            if 'total_relationships' in stats and stats['total_relationships']:
                for stat in stats['total_relationships']:
                    print(f"      Total relationships: {stat.get('count', 'N/A')}")

            print(f"   🎯 Successfully inserted {total_inserted} triples into knowledge graph")

            print_subsection("Sample Graph Query")
            # Demonstrate a sample query
            entities = client.execute_query(
                "MATCH (e:Entity) RETURN e.name as name, e.type as type LIMIT 5"
            )

            print("   📋 Sample entities in graph:")
            for entity in entities:
                print(f"      • {entity['name']} ({entity['type']})")

            return True

    except Exception as e:
        print(f"   ❌ Memgraph integration failed: {str(e)}")
        print("      Make sure Memgraph is running:")
        print("      docker run -p 7687:7687 memgraph/memgraph")
        return False


def display_summary(extraction_results: List[ExtractionResult],
                   exported_files: Dict[str, Path],
                   memgraph_success: bool,
                   output_manager: OutputManager):
    """
    Step 8: Summary and Next Steps
    ===============================

    Display a comprehensive summary of what was accomplished and
    provide guidance for next steps.

    Args:
        extraction_results: List of extraction results
        exported_files: Dictionary of exported file paths
        memgraph_success: Whether Memgraph integration succeeded
        output_manager: OutputManager for directory info
    """
    print_section("STEP 8: Summary and Next Steps",
                  "Complete pipeline execution summary")

    print_subsection("Pipeline Execution Summary")

    # Calculate totals
    total_docs = len(extraction_results)
    total_triples = sum(len(result.triples) for result in extraction_results)
    total_entities = len(set(
        triple.entity_subject for result in extraction_results for triple in result.triples
    ).union(set(
        triple.entity_object for result in extraction_results for triple in result.triples
    )))

    print(f"   📊 Processing Results:")
    print(f"      Documents processed: {total_docs}")
    print(f"      Total triples extracted: {total_triples}")
    print(f"      Unique entities identified: {total_entities}")
    print(f"      Average triples per document: {total_triples/total_docs:.1f}")

    print(f"\n   📁 Organized Output Structure:")
    session_info = output_manager.get_session_info()
    print(f"      Session ID: {session_info['session_id']}")
    print(f"      Output directory: {session_info['outputs_dir']}")
    print(f"      Session directory: {session_info['session_dir']}")

    print(f"\n   💾 Files Generated:")
    for format_name, filepath in exported_files.items():
        if filepath.exists():
            size = filepath.stat().st_size
            print(f"      ✅ {format_name.upper()}: {filepath.name} ({size} bytes)")
            print(f"         Location: {filepath}")
        else:
            print(f"      ❌ {format_name.upper()}: {filepath} (not found)")

    print(f"\n   🔗 Graph Database:")
    if memgraph_success:
        print(f"      ✅ Memgraph integration successful")
        print(f"      ✅ {total_triples} triples stored in knowledge graph")
    else:
        print(f"      ❌ Memgraph integration skipped/failed")

    print_subsection("Architecture Demonstrated")
    print("   🏗️ Complete Version 1 pipeline executed:")
    print("      ✅ Text Input → Sample data loaded")
    print("      ✅ LLM Function Calling → Entities and relationships extracted")
    print("      ✅ Pydantic Validation → Data validated and structured")
    print("      ✅ Tabular Schema Generation → 14-column format created")
    print("      ✅ Quality Metrics → Comprehensive analysis performed")
    print("      ✅ Export Functionality → CSV/JSON files generated")
    if memgraph_success:
        print("      ✅ Graph Storage → Knowledge graph populated")
    else:
        print("      ⚠️  Graph Storage → Optional (requires Memgraph setup)")

    print_subsection("Next Steps")
    print("   🚀 What you can do next:")
    print("      1. Navigate to the outputs directory to review organized files:")
    print(f"         cd {session_info['outputs_dir']}")
    print("      2. Review the CSV/JSON exports in the exports directory")
    print("      3. Check the session summary for detailed results overview")
    print("      4. Experiment with your own text inputs")
    print("      5. Set up Memgraph for knowledge graph features:")
    print("         docker run -p 7687:7687 memgraph/memgraph")
    print("      6. Try the interactive Marimo interface:")
    print("         uv run marimo edit notebooks/v1_llm_function_calling/graph_ingestion_v1.py")
    print("      7. Customize the ontology for your specific domain")
    print("      8. Scale up to Version 2 for larger documents")

    print_subsection("Understanding the Output Organization")
    print("   📋 New organized structure demonstrated:")
    print("      • outputs/extractions/v1/ - Raw extraction results")
    print("      • outputs/quality_reports/v1/ - Quality analysis reports")
    print("      • outputs/exports/csv/ - CSV format exports")
    print("      • outputs/exports/json/ - JSON format exports")
    print("      • outputs/quality_reports/sessions/ - Individual session tracking")
    print("      • Automatic session tracking and cleanup utilities")
    print("      • Consistent file naming with timestamps")

    print_subsection("Key Concepts Demonstrated")
    print("   📋 Technical achievements:")
    print("      • Entity Types: 7 predefined categories (Person, Organization, etc.)")
    print("      • Relationship Types: 9 predefined relationships (works_for, manages, etc.)")
    print("      • 14-Column Schema: Complete metadata preservation")
    print("      • Quality Metrics: Confidence scoring and validation")
    print("      • Organized Output: Structured file management system")
    print("      • Session Tracking: Individual run organization and cleanup")


def main():
    """
    Main function that orchestrates the complete Version 1 pipeline demonstration.

    This function calls each step in sequence, showing the complete flow from
    freeform text input to structured knowledge graph output.
    """
    print("🚀 Version 1: LLM + Function Calling - Complete Pipeline Demonstration")
    print("=" * 80)
    print("This script demonstrates the end-to-end Version 1 pipeline:")
    print("• Load sample texts")
    print("• Extract entities and relationships using LLM function calling")
    print("• Validate and structure the results")
    print("• Generate quality metrics")
    print("• Export to CSV/JSON formats")
    print("• Store in Memgraph knowledge graph (optional)")
    print("=" * 80)

    try:
        # Initialize output manager for organized file handling
        output_manager = OutputManager()

        # Step 1: Load sample data
        sample_data = load_sample_data()

        # Step 2: Demonstrate ontology
        demonstrate_ontology()

        # Step 3: Setup LLM extractor
        extractor = setup_llm_extractor()

        # Step 4: Perform extraction
        extraction_results = perform_extraction(extractor, sample_data)

        if not extraction_results:
            print("❌ No extraction results to process. Exiting.")
            return

        # Step 5: Analyze quality metrics
        quality_report = analyze_quality_metrics(extraction_results)

        # Step 6: Export results using organized structure
        exported_files = export_results(extraction_results, quality_report, output_manager)

        # Step 7: Test Memgraph integration (optional)
        memgraph_success = test_memgraph_integration(extraction_results)

        # Step 8: Display summary
        display_summary(extraction_results, exported_files, memgraph_success, output_manager)

        print("\n🎉 Version 1 pipeline demonstration completed successfully!")
        print("Check the exported files and review the console output to understand the process.")

    except KeyboardInterrupt:
        print("\n\n⚠️ Pipeline demonstration interrupted by user.")
    except Exception as e:
        print(f"\n\n❌ Pipeline demonstration failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
