#!/usr/bin/env python3
"""
Demo Usage - Refactored Pipeline
================================

This demonstrates the new focused, single-purpose function approach
for converting freeform text to knowledge graphs.

Workflow:
1. Row (freeform_text, metadata) → JSON (freeform_text, metadata)
2. JSON (freeform_text, metadata) → JSON (triplets) 
3. JSON (triplets) → Knowledge Graph
4. JSON (triplets) → DataFrame
5. DataFrame → CSV Export
"""

import sys
import pandas as pd
import json
from pathlib import Path

# Add src to path
sys.path.append("src")

from core.pipeline import row_to_json, extract_triples, append_triples_to_dataframe, export_dataframe_to_csv, load_text
from config.llm_config import LLM_CONFIGS, get_llm_config
from ontology.loader import load_ontology_from_cypher


def main(verbose=False, ontology_path="examples/ontologies/default_ontology.cypher", text_data_path="data/samples/sample_texts.csv", skip_llm=False):
    """Complete workflow demonstration"""
    print("🚀 Refactored Pipeline Demo")
    print("=" * 50)

    # Load ontology from example file
    print("📋 Loading ontology...")
    try:
        ontology = load_ontology_from_cypher("examples/ontologies/default_ontology.cypher")
        print(f"   ✅ Loaded ontology with {len(ontology['entities'])} entities and {len(ontology['relationships'])} relationships")
    except Exception as e:
        print(f"   ❌ Failed to load ontology: {e}")
        return

    # Sample input data
    print("📄 Loading sample input data:")
    text_rows = load_text(text_data_path)

    print(f"📄 Loaded {len(text_rows)} sample text rows")
    if verbose:
        for row in text_rows:
            print(f"   - {row['UID']}: {row['freeform_text'][:50]}...")

    print(f"📄 Processing {len(text_rows)} sample texts...")
    
    # Initialize containers
    all_triples = []
    df = None  # Will be initialized by create_triples_dataframe()
    
    for i, row in enumerate(text_rows, 1):
        print(f"\n--- Processing Row {i}: {row['UID']} ---")
        
        # Step 1: Row processing ✅ IMPLEMENTED
        print("1️⃣ Converting row to standardized JSON...")
        text_json = row_to_json(row)
        print(f"   ✅ Standardized: {text_json['document_id']}")
        if verbose:
            print(f"   - JSON:")
            print("   {")
            for key, value in text_json.items():
                print(f"      '{key}': {value!r},")
            print("   }")
        
        # Step 2: Extract triples using LLM
        print("2️⃣ Extracting triples with LLM...")
        try:
            # Test with Ollama for demo (use available model)
            llm_config = get_llm_config(provider="anthropic", model="claude-3-5-sonnet-20241022")
            # llm_config = get_llm_config(provider="ollama", model="devstral:latest")
            print(f"   ✅ LLM Config: {llm_config['provider']} - {llm_config['model']}")

            if not skip_llm:
                # Extract triples using the new function
                triples_array = extract_triples(text_json, llm_config, ontology)
                print(f"   ✅ Extracted {len(triples_array)} triples")

                if verbose:
                    print(f"   - Triples:")
                    for triple in triples_array:
                        print("      {")
                        for key, value in triple.items():
                            print(f"         {key}: {value}")
                        print("      }")
            else:
                print("   ⚠️ Skipping LLM extraction (for testing)")
                triples_array = []


        except (ValueError, EnvironmentError) as e:
            print(f"   ❌ LLM Config failed: {e}")
            print("   💡 Check .env.example for required environment variables")
            # Don't continue with fake success - let it fail properly
            continue  # Skip to next row
        except RuntimeError as e:
            print(f"   ❌ LLM Extraction failed: {e}")
            print("   💡 Check your API key and model availability")
            continue  # Skip to next row
        
        # Step 3: Add to knowledge graph
        print("3️⃣ Adding triples to knowledge graph...")
        # graph_success = append_triples_to_graph(triples_json, graph_client)
        # print(f"   ✅ Graph insertion: {'Success' if graph_success else 'Failed'}")
        
        # Step 4: Add to DataFrame
        print("4️⃣ Adding triples to DataFrame...")
        df = append_triples_to_dataframe(triples_array, df)
        print(f"   ✅ DataFrame now has {len(df)} rows")
        
        # Store for summary
        # all_triples.append(triples_json)
    
    # Step 5: Export to CSV
    print("\n5️⃣ Exporting DataFrame to CSV...")
    if df is not None and len(df) > 0:
        output_file = export_dataframe_to_csv(df)  # Uses timestamped default path
        print(f"   ✅ Exported to: {output_file}")
    else:
        print("   ⚠️ No data to export")
    
    # Summary
    print(f"\n🎉 Demo Complete!")
    print(f"   - Processed: {len(text_rows)} documents")
    # print(f"   - Total triples: {sum(t['total_triples'] for t in all_triples)}")
    # print(f"   - DataFrame rows: {len(df)}")
    # print(f"   - Output file: {output_file}")





if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Demo Usage - Refactored Pipeline')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--ontology_path', type=str, help='Path to ontology file', default="examples/ontologies/default_ontology.cypher")
    parser.add_argument('--text_data_path', type=str, help='Path to CSV file', default="data/samples/sample_texts.csv")
    parser.add_argument('--skip_llm', action='store_true', help='Skip LLM processing (for testing)')
    args = parser.parse_args()
    
    verbose = args.verbose

    main(verbose=verbose, ontology_path=args.ontology_path, text_data_path=args.text_data_path, skip_llm=args.skip_llm)
