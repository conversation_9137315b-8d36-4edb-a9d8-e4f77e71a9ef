# Environment Setup for Graph Ingestion Pipeline

## Required Environment Variables

### LLM API Keys (At least one required)

```bash
# OpenAI API Key
OPENAI_API_KEY=sk-your-openai-key-here

# Anthropic API Key  
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
```

**Note**: You need at least one of these API keys for LLM extraction to work.

## Optional Environment Variables

### Memgraph Database (Optional - defaults to localhost)

```bash
MEMGRAPH_HOST=localhost
MEMGRAPH_PORT=7687
MEMGRAPH_USERNAME=
MEMGRAPH_PASSWORD=
```

### Model Preferences (Optional - uses provider defaults)

```bash
DEFAULT_OPENAI_MODEL=gpt-4o-mini
DEFAULT_ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
```

### Processing Settings (Optional)

```bash
DEFAULT_CONFIDENCE_THRESHOLD=0.7
MAX_RETRIES=3
EXTRACTION_TIMEOUT=120
```

## Setup Instructions

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` and add your API keys:**
   ```bash
   # Add at least one of these:
   OPENAI_API_KEY=your-actual-openai-key
   ANTHROPIC_API_KEY=your-actual-anthropic-key
   ```

3. **Test your setup:**
   ```bash
   uv run python demo_usage.py
   ```

## Production-Ready Error Handling

The pipeline follows **fail-fast** principles:

- ❌ **Missing API keys** → Clear error message, no hardcoded fallbacks
- ❌ **Invalid configuration** → Immediate failure with specific error
- ❌ **Missing config files** → Explicit error, no silent defaults

**This is intentional** - the system should fail clearly when requirements aren't met rather than masking problems with hardcoded defaults.

## Configuration Architecture

### ✅ **Correct Usage (Import from config):**
```python
from config.llm_config import LLM_CONFIGS, get_llm_config

# Static config access
config = LLM_CONFIGS["openai"]

# Environment-based config with validation
config = get_llm_config("openai")  # Validates API key exists
config = get_llm_config()          # Auto-detects from available keys
```

### ❌ **Deprecated Usage (Function generation):**
```python
# DON'T USE - deprecated approach
config = create_llm_config("openai")  # Old function-based approach
```

## Troubleshooting

### "Required environment variable OPENAI_API_KEY is not set"
- Add your API key to `.env` file
- Make sure `.env` is in the project root directory
- Restart your terminal/IDE after adding the key

### "No LLM API keys found"
- You need at least one API key (OpenAI or Anthropic)
- Check that your `.env` file has the correct variable names
- Verify the API key format is correct (starts with `sk-` for OpenAI, `sk-ant-` for Anthropic)

### Import errors for config modules
- Make sure you're running from the project root directory
- Check that `src/config/` directory exists with the config files
