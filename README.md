# Knowledge Graph Construction from Freeform Text

## 🎯 Current System Overview

**Status: ✅ Refactored Architecture Complete - Production-Ready Ontology-Driven Extraction**

This system transforms unstructured text into queryable knowledge graphs using LLM-powered entity and relationship extraction with strict ontology adherence. The refactored architecture uses **Cypher files as the single source of truth** for ontology definitions, ensuring consistent extraction across all components.

### ✅ Refactored Architecture

#### Core Pipeline Functions (Single-Purpose)
```
Foundation Layer Functions
├── load_sample_data() - CSV/JSON input with flexible column mapping
├── row_to_json() - Standardize input data
├── extract_triples() - LLM-powered extraction with ontology validation
├── append_triples_to_dataframe() - Structured data export
├── export_dataframe_to_csv() - Timestamped file output
└── append_triples_to_graph() - Knowledge graph population (optional)

LLMClient Class (Lightweight)
├── Unified LiteLLM interface for OpenAI, Anthropic, Ollama
├── Native function calling registration for Ollama models
├── Provider-specific model formatting
└── Automatic API key management

Ontology System (Cypher-Driven)
├── Cypher files as single source of truth
├── Dynamic Pydantic validation generation
├── Function schema generation for LLM tools
└── Prompt text generation with strict constraints
```

### Previous Project Structure - OLD STATE, TO BE REMOVED AFTER FULL REFACTOR

```
graph_ingestion/
├── notebooks/v1_llm_function_calling/     # Version 1 implementation
│   ├── graph_ingestion_v1_standard.py     # End-to-end demo script ⭐
│   ├── graph_ingestion_v1.py              # Interactive Marimo notebook
│   ├── test_extraction.py                 # Comprehensive tests
│   └── README.md                           # Version 1 guide
├── src/                                    # Core implementation
│   ├── config/                             # Configuration management ✨
│   │   ├── llm_config.py                   # LLM provider configurations
│   │   ├── env_config.py                   # Environment variable documentation
│   │   └── __init__.py                     # Config package with dotenv loading
│   ├── core/                               # Foundation layer functions ✨
│   │   └── pipeline.py                     # Single-purpose pipeline functions
│   ├── ontology/models.py                  # Pydantic schemas
│   ├── extraction/llm_extractor.py        # LLM integration
│   ├── validation/quality_metrics.py      # Quality analysis
│   ├── graph_construction/memgraph_client.py  # Graph database
│   └── utils/output_manager.py             # Organized file management
├── outputs/                                # Organized output structure ✨
│   ├── extractions/v1/                     # Raw extraction results
│   ├── quality_reports/v1/                 # Quality analysis reports
│   ├── quality_reports/sessions/           # Individual session tracking
│   └── exports/                            # Final exports
│       ├── csv/                            # CSV format files
│       ├── json/                           # JSON format files
│       └── archives/                       # Compressed archives
├── data/samples/                           # Sample texts
├── dev_tests/                              # Development tests ✨
│   └── test_foundation_layer.py            # Foundation layer function tests
├── demo_usage.py                           # Refactored pipeline demo ✨
├── test_core_functionality.py             # Core tests (no API needed)
├── .env.example                            # Environment template
├── ENVIRONMENT_SETUP.md                    # Environment setup guide ✨
├── DEVELOPMENT_STATUS.md                   # Development progress tracking ✨
└── README.md                               # This file

## 🚀 Quickstart Guide

### Prerequisites
- Python 3.13+
- **At least one LLM provider:**
  - Anthropic API key (recommended for production), OR
  - OpenAI API key, OR
  - Ollama running locally

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd graph_ingestion

# Install dependencies
uv sync

# Copy environment template and configure your LLM provider
cp .env.example .env
# Edit .env with your API keys (see Environment Setup below)
```

### Environment Setup (.env file)

```bash
# LLM Provider API Keys (need at least one)
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here  # Recommended for production
OPENAI_API_KEY=sk-your-openai-key-here
# Ollama requires no API key - just running service

# Optional: Model Preferences (uses provider defaults if not set)
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
OPENAI_MODEL=gpt-4o-mini
OLLAMA_MODEL=llama3.2:latest

# Optional: Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
```

### Basic Usage

```bash
# Run the complete demo with auto-detected LLM provider
uv run python demo_usage.py --verbose

# Test foundation layer functions (no API keys needed)
uv run python dev_tests/test_foundation_layer.py

# Test ontology generation from Cypher files
uv run python dev_tests/test_ontology_generation.py
```



### Example Usage

```python
# Core pipeline usage
from src.core.pipeline import load_sample_data, row_to_json, extract_triples, append_triples_to_dataframe, export_dataframe_to_csv
from src.config.llm_config import get_llm_config
from ontology.loaders import load_ontology

# Load ontology from Cypher file (single source of truth)
ontology = load_ontology("examples/ontologies/default_ontology.cypher")

# Configure LLM (auto-detects available provider)
llm_config = get_llm_config()  # or specify: get_llm_config(provider="anthropic")

# Load data from CSV/JSON (flexible column naming)
data_rows = load_sample_data("data/samples/sample_texts.csv")  # or .json file

# Process multiple documents
df = None
for row in data_rows:
    # Standardize and extract
    text_json = row_to_json(row)
    triples = extract_triples(text_json, llm_config, ontology)

    # Accumulate in DataFrame
    df = append_triples_to_dataframe(triples, df)

# Export with timestamp
output_file = export_dataframe_to_csv(df)  # outputs/demo/demo_results_YYYYMMDD_HHMMSS.csv

# Result: Complete pipeline from CSV input to organized CSV output
```

## 🔧 Ontology System

The system uses **Cypher files as the single source of truth** for ontology definitions. This ensures consistency across prompt generation, Pydantic validation, and function schemas.

### Ontology File Format

**Location**: `examples/ontologies/default_ontology.cypher`

```cypher
// Entity Types
CREATE (:EntityType {
  name: "Person",
  description: "Individual human beings, employees, contacts",
  properties: ["name", "email", "title", "department"]
});

CREATE (:EntityType {
  name: "Organization",
  description: "Companies, departments, teams, institutions",
  properties: ["name", "type", "industry", "location"]
});

// Relationship Types
CREATE (:RelationshipType {
  name: "works_at",
  description: "Person employed by Organization",
  source_entities: ["Person"],
  target_entities: ["Organization"],
  properties: ["since_date", "role", "department"]
});
```

### Custom Ontology Creation

```python
# Load custom ontology
ontology = load_ontology("path/to/your_ontology.cypher")

# The system automatically generates:
# 1. Prompt text with entity/relationship definitions
# 2. Pydantic validation enums from ontology
# 3. Function calling schemas for LLM tools
# 4. Validation rules for extracted triples

# Use with any LLM provider
triples = extract_triples(text_json, llm_config, ontology)
```

## 🤖 Model Configuration

### Auto-Detection Priority

The system **auto-detects** available LLM providers in this order:

1. **Anthropic** (if `ANTHROPIC_API_KEY` is set)
2. **OpenAI** (if `OPENAI_API_KEY` is set)
3. **Ollama** (if service running at `http://localhost:11434`)

### Manual Model Selection

**Environment Variables** (in `.env` file):
```bash
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
OPENAI_MODEL=gpt-4o-mini
OLLAMA_MODEL=llama3.2:latest
```

**Function Parameter Override** (highest priority):
```python
# Override environment settings
llm_config = get_llm_config(provider="anthropic", model="claude-3-haiku-20240307")
llm_config = get_llm_config(provider="ollama", model="deepseek-r1:latest")
llm_config = get_llm_config(provider="openai", model="gpt-4")
```

### Model Performance Comparison

| Model | Schema Adherence | JSON Quality | Extraction Quality | Cost |
|-------|-----------------|--------------|-------------------|------|
| **Claude 3.5 Sonnet** | ✅ Excellent | ✅ Perfect | ✅ High | $$$ |
| **GPT-4** | ✅ Good | ✅ Good | ✅ High | $$$ |
| **Ollama (devstral:latest)** | ✅ Good | ✅ Good | ✅ Good | Free |
| **Ollama (llama3.2)** | ❌ Poor | ❌ Inconsistent | ⚠️ Variable | Free |

**Recommendation**: Use **Claude 3.5 Sonnet** for production - it consistently follows ontology constraints and produces high-quality extractions.

## ⚠️ Known Issues and Fixes

### Ollama Model Limitations

**Issue**: Ollama models may not follow schema constraints strictly
- **Symptoms**: Creates invalid relationships like "will be held in" instead of "located_in"
- **Root Cause**: Local models struggle with enum constraints in function calling
- **Fix Applied**: Automatic model registration for native function calling support
- **Recommendation**: Use Claude 3.5 Sonnet for production workloads

### JSON Parsing Issues

**Issue**: Some Ollama models return malformed JSON
- **Symptoms**: `Expecting ',' delimiter` errors
- **Fix Applied**: Automatic JSON repair logic handles common formatting issues
- **Fallback**: Manual retry with corrected prompts

### Auto-Detection Priority

**Issue**: Auto-detection prioritizes OpenAI over Ollama even with placeholder keys
- **Symptoms**: Authentication errors when placeholder API keys exist
- **Workaround**: Use explicit provider selection: `get_llm_config(provider="ollama")`
- **Future Fix**: Validate API key authenticity before selection

## 🔮 Future Improvements

### Performance Optimizations
- **LLMClient Instantiation**: Pass client object to `extract_triples()` instead of `llm_config` to avoid repeated instantiation
- **Connection Pooling**: Implement connection pooling for better performance with multiple requests
- **Batch Processing**: Support for processing multiple documents in parallel

### Reliability Enhancements
- **Retry Logic**: Add configurable retry logic for failed extractions
- **Streaming Responses**: Support for streaming responses for large documents
- **Graceful Degradation**: Fallback to simpler models when primary model fails

### Scalability Features
- **Distributed Processing**: Support for distributed extraction across multiple workers
- **Caching Layer**: Cache ontology parsing and model loading for faster startup
- **Memory Optimization**: Optimize memory usage for large document processing

## 🧪 Development and Testing

### Foundation Layer Tests

```bash
# Test core pipeline functions (no API keys needed)
uv run python dev_tests/test_foundation_layer.py

# Test ontology generation from Cypher files
uv run python dev_tests/test_ontology_generation.py

# Test JSON repair functionality
uv run python dev_tests/test_json_repair.py
```

### Integration Tests

```bash
# Run complete demo with LLM provider
uv run python demo_usage.py --verbose

# Test with specific providers
ANTHROPIC_API_KEY=your-key uv run python demo_usage.py
OLLAMA_MODEL=deepseek-r1:latest uv run python demo_usage.py
```

### Adding New LLM Providers

1. **Add provider configuration** in `src/config/llm_config.py`:
```python
"new_provider": {
    "provider": "new_provider",
    "model": os.getenv("NEW_PROVIDER_MODEL", "default-model"),
    "api_key_env": "NEW_PROVIDER_API_KEY",
    # ... other config
}
```

2. **Add model formatting** in `src/core/llm_client.py`:
```python
elif self.provider == "new_provider":
    self.litellm_model = f"new_provider/{self.model_name}"
```

3. **Update auto-detection** in `_detect_provider_from_env()` function

4. **Test integration** with the demo script

## 📊 What You Get

### Input Examples

**CSV Format** (flexible column naming):
```csv
unique_ID,text
DOC_001,"Sarah Johnson, the VP of Engineering at TechCorp, is leading the AI initiative project in collaboration with DataSoft."
DOC_002,"The quarterly meeting will be held in Conference Room A with all department heads attending."
```

**JSON Format**:
```json
[
  {
    "UID": "DOC_001",
    "freeform_text": "Sarah Johnson, the VP of Engineering at TechCorp, is leading the AI initiative project in collaboration with DataSoft.",
    "metadata": {"source": "company_announcement"}
  }
]
```

### Extracted Output (Claude 3.5 Sonnet)
```json
[
  {
    "entity_subject": "Sarah Johnson",
    "relationship_type": "works_at",
    "entity_object": "TechCorp",
    "entity_subject_type": "Person",
    "entity_object_type": "Organization",
    "confidence_score": 0.85,
    "source_text_snippet": "Sarah Johnson, the VP of Engineering at TechCorp",
    "relationship_context": "Works as VP of Engineering",
    "extraction_method": "litellm_function_calling",
    "document_id": "DOC_001",
    "extraction_timestamp": "2025-07-15T22:20:31.071121"
  },
  {
    "entity_subject": "Sarah Johnson",
    "relationship_type": "holds_role",
    "entity_object": "VP of Engineering",
    "entity_subject_type": "Person",
    "entity_object_type": "Role",
    "source_text_snippet": "Sarah Johnson, the VP of Engineering at TechCorp",
    "relationship_context": "Current role at TechCorp"
  }
]
```

**Output (CSV/JSON) - FUTURE STATE:**
| entity_subject | relationship_type | entity_object | entity_subject_type | entity_object_type | confidence_score | source_text_snippet |
|---|---|---|---|---|---|---|
| Sarah Johnson | holds_role | VP of Engineering | Person | Role | 0.95 | Sarah Johnson, the VP of Engineering |
| Sarah Johnson | works_for | TechCorp | Person | Organization | 0.92 | VP of Engineering at TechCorp |
| TechCorp | collaborates_with | DataSoft | Organization | Organization | 0.85 | in collaboration with DataSoft |

**Plus:** Quality metrics, validation reports, and optional Memgraph knowledge graph.

### Output Formats
- **JSON**: Structured triples with full metadata
- **CSV**: Tabular format for analysis and import
  - **Location**: `outputs/demo/demo_results_YYYYMMDD_HHMMSS.csv`
  - **Organization**: Timestamped files prevent overwriting
- **Knowledge Graph**: Memgraph database (optional)
- **Quality Metrics**: Extraction confidence and validation reports

## 📂 Output Organization - FUTURE STATE

The system now uses an organized directory structure instead of dumping files in the project root:

```
outputs/
├── extractions/v1/                     # Raw extraction results by version
│   └── v1_extraction_results_20250709_143022.json
├── quality_reports/v1/                 # Quality analysis reports by version  
│   └── v1_quality_report_20250709_143022.json
├── quality_reports/sessions/           # Individual session tracking
│   └── 20250709_143022/                # Session ID (timestamp)
│       ├── v1_quality_report_20250709_143022.json
│       └── session_summary.json       # Complete session overview
└── exports/                            # Final exports for external use
    ├── csv/v1_extraction_results_20250709_143022.csv
    ├── json/v1_extraction_results_20250709_143022.json
    └── archives/                       # Future: compressed exports
```

**Benefits:**
- 🗂️ **Organized by type**: Separate directories for extractions, reports, and exports
- 📅 **Session tracking**: Each run gets a unique session ID for easy tracking
- 🔄 **Version separation**: V1, V2, V3 results stored separately
- 🧹 **Easy cleanup**: Built-in utilities to manage old sessions
- 📊 **Session summaries**: Comprehensive overview of each pipeline run

## 🛠️ Troubleshooting

### Common Issues

**"No LLM providers found"**
- **Solution**: Set at least one API key in `.env` or start Ollama service
- **Check**: Verify API key format and permissions

**"Authentication error" with valid API key**
- **Cause**: Auto-detection may choose wrong provider
- **Solution**: Use explicit provider: `get_llm_config(provider="anthropic")`

**Ollama connection issues**
- **Check service**: `curl http://localhost:11434/api/tags`
- **Start service**: `ollama serve`
- **Pull model**: `ollama pull llama3.2:latest`

**Schema validation errors**
- **Cause**: LLM not following ontology constraints (common with Ollama)
- **Solution**: Switch to Claude 3.5 Sonnet for better constraint adherence
- **Debug**: Check LLM response in verbose mode

**JSON parsing errors**
- **Cause**: Malformed JSON from some Ollama models
- **Fix**: Automatic JSON repair is implemented
- **Fallback**: Use Claude or GPT models for reliable JSON output

---

## 🎯 System Status

**✅ Production-Ready Features:**
- ✅ **Ontology-Driven Extraction**: Cypher files as single source of truth
- ✅ **Multi-Provider LLM Support**: OpenAI, Anthropic, Ollama with auto-detection
- ✅ **Strict Schema Validation**: Pydantic validation with ontology constraints
- ✅ **Native Function Calling**: Proper tool calling for all providers
- ✅ **JSON Repair Logic**: Handles malformed responses automatically
- ✅ **Comprehensive Testing**: Foundation layer and integration tests

**🔧 Recommended Configuration:**
- **Production**: Claude 3.5 Sonnet (best constraint adherence)
- **Development**: Ollama with deepseek-r1 or llama3.2
- **Cost-Effective**: GPT-4o-mini for balanced performance/cost

**📈 Performance Benchmarks:**
- **Claude 3.5 Sonnet**: 95%+ ontology adherence, perfect JSON
- **GPT-4**: 90%+ ontology adherence, reliable JSON
- **Ollama Models**: 60-80% adherence, requires JSON repair

## 📁 Project Structure

```
graph_ingestion/
├── src/
│   ├── config/llm_config.py              # LLM provider configurations
│   ├── core/
│   │   ├── pipeline.py                    # Core extraction functions
│   │   └── llm_client.py                  # LiteLLM integration
│   └── ontology/
│       ├── loaders.py                     # Cypher file loading
│       └── generators.py                  # Schema generation
├── data/samples/                          # Input data
│   ├── sample_texts.csv                   # CSV input (unique_ID, text)
│   └── sample_texts.json                  # JSON input (alternative)
├── examples/ontologies/
│   └── default_ontology.cypher            # Default ontology definition
├── outputs/demo/                          # Organized output structure
│   └── demo_results_YYYYMMDD_HHMMSS.csv   # Timestamped results
├── dev_tests/                             # Development tests
│   ├── test_foundation_layer.py
│   ├── test_ontology_generation.py
│   └── test_json_repair.py
├── demo_usage.py                          # Main demo script
├── .env.example                           # Environment template
└── README.md                              # This file
```

## 🚀 Getting Started

1. **Clone and install**:
   ```bash
   git clone <repository-url>
   cd graph_ingestion
   uv sync
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Add your API keys to .env
   ```

3. **Run the demo**:
   ```bash
   uv run python demo_usage.py --verbose
   ```

4. **Customize ontology**:
   - Edit `examples/ontologies/default_ontology.cypher`
   - Add your entity types and relationships
   - Test with your own text data

## 📚 Additional Resources

- **Foundation Layer Tests**: `dev_tests/test_foundation_layer.py`
- **Ontology Examples**: `examples/ontologies/`
- **Configuration Guide**: See `.env.example` for all options
- **LLM Provider Docs**: Check provider-specific documentation for API keys

---

**Transform unstructured text into queryable knowledge graphs with confidence. The system is production-ready with Claude 3.5 Sonnet providing excellent ontology adherence and extraction quality.**







---

## 🎯 Current Status

**✅ Foundation Layer (Refactored Pipeline)**
- ✅ **Function 1**: `process_text_row()` - Row to standardized JSON conversion
- ✅ **LLM Configuration**: Proper config files with auto-detection
- ✅ **Three LLM Providers**: OpenAI, Anthropic, and Ollama support
- ✅ **Environment Management**: python-dotenv integration with `.env` files
- ✅ **Production-Ready**: Fail-fast error handling, no hardcoded defaults
- ✅ **Demo Pipeline**: `demo_usage.py` with verbose mode

**✅ Version 1: LLM + Function Calling**
- Supports OpenAI, Anthropic, and Ollama
- Complete 14-column tabular schema
- Quality metrics and validation
- Organized CSV/JSON export with session tracking
- Interactive Marimo interface
- Structured output management system
- **Tested and working!**

**🔄 Next Steps:**
- Function 3: `create_triples_dataframe()` - DataFrame schema initialization
- Function 4: `create_graph_client()` - Graph database client setup
- Ontology schema alignment and `load_ontology()` function

**🔮 Planned:** Version 2 (Chunked Pipeline) and Version 3 (Hybrid NER + LLM)

---

## Problem Statement

Extract entities and relationships from unstructured freeform text according to a predefined ontology and populate both a Knowledge Graph (Memgraph) and structured tables in Databricks. The solution must handle arbitrary ontologies, scale to production volumes, and provide rich metadata for downstream GraphRAG (graph retrieval-augmented generation) use cases.

## Approach

This repository implements a **two-step progressive approach**:

1. **Freeform Text → Tabular**: Extract entities and relationships from natural language into structured entity-relationship tables
2. **Tabular → Knowledge Graph**: Transform structured tables into a queryable graph database

Three progressive versions are implemented, each optimizing for different requirements: speed (V1), scale (V2), and cost-effectiveness (V3).

## Feature Comparison

| Feature | Version 1: LLM + Function Calling | Version 2: Chunked Pipeline | Version 3: Hybrid NER + LLM |
|---------|-----------------------------------|----------------------------|----------------------------|
| **Text Complexity** | Simple paragraphs (<1000 words) | Complex documents (1000+ words) | Complex documents (1000+ words) |
| **Processing Speed** | Fast (single API call) | Medium (parallel chunks) | Fast (reduced LLM calls) |
| **Cost per 1K Words** | High ($0.50-1.00) | High ($0.75-1.50) | Medium ($0.25-0.75) |
| **Expected Accuracy** | 75-85% entities, 70-80% relationships | 80-90% entities, 75-85% relationships | 85-95% entities, 80-90% relationships |
| **Tabular Extraction** | Complete 14-column schema | Complete + chunk tracking | Complete + confidence optimization |
| **Scalability** | Limited (token constraints) | High (distributed processing) | High (optimized processing) |
| **Infrastructure** | Marimo (local development) | Marimo → Databricks | Marimo → Databricks |
| **Production Readiness** | Prototype/POC | Beta/Production | Production |
| **Entity Resolution** | None | Cross-chunk deduplication | Advanced fuzzy matching |
| **GraphRAG Ready** | Basic metadata | Complete traceability | Advanced confidence scoring |

## Ontology Definition

### Entity Types
- **Person**: Individuals (employees, customers, contacts)
- **Organization**: Companies, departments, teams
- **Location**: Offices, cities, buildings, regions
- **Project**: Initiatives, programs, research projects
- **Event**: Meetings, launches, incidents, conferences
- **Product**: Software, platforms, services, tools
- **Role**: Job titles, positions, responsibilities

### Relationship Types
- **works_for**: Person → Organization
- **located_at**: Person/Organization → Location
- **manages**: Person → Project/Person
- **participates_in**: Person → Event/Project
- **reports_to**: Person → Person
- **collaborates_with**: Person/Organization ↔ Person/Organization
- **occurred_at**: Event → Location
- **develops**: Person/Organization → Product
- **holds_role**: Person → Role

### Sample Data Examples

#### Freeform Text Input:
```
UID 1: "Sarah Johnson, the VP of Engineering at TechCorp, is leading the AI initiative project in collaboration with DataSoft. The project kicked off last month at the San Francisco headquarters."

UID 2: "The quarterly review meeting was held on March 15th where Mike Chen presented the new customer analytics platform. The platform was developed by his team in partnership with external consultants from InnovateAI."

UID 3: "Elena Rodriguez, who works as a data scientist at CloudVentures, recently moved to the Austin office. She's currently working on the predictive modeling project alongside the Seattle team."
```

#### Extracted Tabular Output:
| entity_subject | relationship_type | entity_object | entity_subject_type | entity_object_type | confidence_score | source_text_snippet | document_id |
|---|---|---|---|---|---|---|---|
| Sarah Johnson | holds_role | VP of Engineering | Person | Role | 0.95 | "Sarah Johnson, the VP of Engineering" | UID_1 |
| Sarah Johnson | works_for | TechCorp | Person | Organization | 0.92 | "VP of Engineering at TechCorp" | UID_1 |
| Sarah Johnson | manages | AI initiative project | Person | Project | 0.88 | "is leading the AI initiative project" | UID_1 |
| TechCorp | collaborates_with | DataSoft | Organization | Organization | 0.85 | "in collaboration with DataSoft" | UID_1 |
| Mike Chen | develops | customer analytics platform | Person | Product | 0.87 | "Mike Chen presented the new customer analytics platform" | UID_2 |
| Elena Rodriguez | located_at | Austin office | Person | Location | 0.89 | "recently moved to the Austin office" | UID_3 |

## Tabular Schema Definition

### Core Triple Attributes (for Graph Construction):
- **entity_subject**: Source entity name
- **relationship_type**: Relationship/edge type from ontology
- **entity_object**: Target entity name
- **entity_subject_type**: Source entity category
- **entity_object_type**: Target entity category

### Metadata Attributes (for GraphRAG & Quality):
- **confidence_score**: Extraction confidence (0.0-1.0)
- **source_text_snippet**: Original text supporting this relationship
- **extraction_method**: Extraction approach used
- **document_id**: Source document identifier
- **chunk_id**: Specific text chunk identifier
- **relationship_context**: Semantic context of relationship
- **extraction_timestamp**: When extraction occurred
- **entity_subject_id**: Unique ID for subject entity (deduplication)
- **entity_object_id**: Unique ID for object entity (deduplication)

## Two-Step Process

### Step 1: Freeform Text → Tabular
**Input**: Unstructured natural language text
**Process**: LLM extraction with structured output validation
**Output**: Structured table with entity-relationship triples + rich metadata

### Step 2: Tabular → Knowledge Graph
**Input**: Structured table from Step 1
**Process**: Entity deduplication, graph node creation, relationship insertion
**Output**: Queryable Memgraph database with full traceability

## Considerations

**Ontology Complexity**: Simple ontologies (5-10 entity types, 10-20 relationship types) work well with all versions. Complex ontologies (20+ entities, 50+ relationships) benefit from Version 3's hybrid approach.

**Text Types**: Email threads and transcripts work well with Version 2's chunking. Technical documentation and reports may need Version 3's precision. Simple business communications work with Version 1.

**Scale Requirements**: <1K texts/month: Version 1 sufficient. 1K-10K texts/month: Version 2 required. 10K+ texts/month: Version 3 for cost optimization.

**GraphRAG Requirements**: High confidence thresholds need Version 3. Basic entity traversal works with Version 1-2. Complex reasoning requires Version 2-3's metadata richness.

**Quality vs. Speed**: Version 1 prioritizes rapid prototyping, Version 2 prioritizes completeness, Version 3 balances quality and cost.

## Technology Stack

- **Marimo**: Local development and prototyping
- **Databricks**: Production deployment, large-scale processing, Delta Lake storage
- **Memgraph**: Graph database with real-time capabilities and advanced querying
- **LLM Providers**:
  - **OpenAI API**: GPT models for text extraction
  - **Anthropic Claude**: Claude models for text extraction
  - **Ollama**: Local LLM deployment (llama3.2, etc.)
- **Pydantic**: Data validation and schema enforcement
- **python-dotenv**: Environment variable management
- **spaCy**: Named Entity Recognition (Version 3)
- **pymgclient**: Memgraph Python driver
- **pandas**: Tabular data processing and manipulation

---

## Version 1 - LLM + Function Calling (Local Only)

### Definition of Done:
- Process simple text passages (<1000 words) with 75-85% entity accuracy and 70-80% relationship accuracy
- Generate complete 14-column tabular schema with basic metadata
- Successfully populate both Memgraph and local storage
- Handle 90% of extraction attempts without manual intervention
- Achieve processing speed of 30-60 seconds per text passage
- Include hardcoded metadata: extraction_method="llm_function_calling", confidence_score=0.85

### High-Level Architecture (Local with Marimo):
```
Text Input → LLM Function Calling → Pydantic Validation →
Tabular Schema Generation → Parallel Storage: [Local Memgraph + CSV/JSON Files] →
Quality Metrics Dashboard
```

---

## Version 2 - Chunked Multi-Step Pipeline

### Definition of Done:
- Process complex documents (1000+ words) with 80-90% entity accuracy and 75-85% relationships
- Implement semantic chunking with overlap management
- Include complete chunk-level metadata: chunk_id, chunk_position, overlap_boundaries
- Achieve entity resolution with 85%+ deduplication accuracy across chunks
- Support batch processing of 10+ documents simultaneously
- Generate comprehensive extraction audit trails

### High-Level Architecture (Local with Marimo):
```
Text Input → Semantic Chunking → Sequential LLM Processing → Entity Resolution Pipeline →
Relationship Merging → Tabular Schema → Local Memgraph + Parquet Files → Quality Validation
```

### High-Level Architecture (Databricks):
```
Text Input → Distributed Chunking → Parallel LLM Processing → Entity Resolution Pipeline →
Relationship Merging → Delta Lake → Memgraph Batch Insert → Production Quality Metrics
```

---

## Version 3 - Hybrid NER + LLM Validation

### Definition of Done:
- Process complex documents with 85-95% entity accuracy and 80-90% relationships
- Achieve 60%+ cost reduction vs. Version 2 while maintaining quality
- Include advanced metadata: ner_model_version, entity_extraction_method, validation_flags
- Support production-scale processing (10K+ texts/month)
- Demonstrate measurable quality improvement over Version 2
- Provide confidence-based filtering for GraphRAG applications

### High-Level Architecture (Local with Marimo):
```
Text Input → Semantic Chunking → spaCy NER Entity Extraction → Custom Entity Mapping →
LLM Relationship Extraction + Validation → Confidence Scoring → Entity Resolution →
Tabular Schema → Local Memgraph + Storage → Quality Monitoring
```

### High-Level Architecture (Databricks):
```
Text Input → Distributed Chunking → Parallel spaCy NER → Custom Entity Mapping →
Distributed LLM Processing → Advanced Confidence Scoring → Entity Resolution →
Delta Lake → Memgraph → Production Monitoring & GraphRAG Optimization
```

---

# Implementation Guide

## Phase 1: Version 1 - Foundation (Local Only)

### 1.1 Environment Setup & Dependencies (Local)
```bash
pip install marimo openai anthropic pydantic pymgclient pandas python-dotenv
```
- Configure API keys in `.env` file
- Install local Memgraph using Docker: `docker run -p 7687:7687 memgraph/memgraph`
- Create basic Marimo notebook structure with sample ontology

### 1.2 Ontology & Schema Definition (Local)
- Implement Pydantic models for entities and relationships
- Create tabular schema validation classes
- Define sample ontology with 7 entity types and 9 relationship types
- Build validation rules and error handling

### 1.3 LLM Integration Pipeline (Local)
- Design function calling schema matching tabular output format
- Create prompt templates incorporating ontology definitions
- Implement retry logic, rate limiting, and progress tracking
- Add structured output validation with Pydantic

### 1.4 Data Storage & Visualization (Local)
- Setup Memgraph schema with proper indexes
- Implement Step 2: tabular → graph transformation functions
- Create CSV/JSON export for validation and backup
- Build Marimo dashboard for extraction results and quality metrics

---

## Phase 2: Version 2 - Scale & Chunking

### 2.1 Advanced Text Processing (Local)
- Implement semantic chunking (sentence/paragraph boundaries)
- Create chunk overlap management and metadata generation
- Build chunking visualization and debugging tools in Marimo
- Add support for various text input formats

### 2.2 Sequential Processing Framework (Local)
- Design sequential LLM processing with progress tracking
- Implement entity resolution using fuzzy matching
- Create relationship merging logic with conflict resolution
- Add comprehensive logging and error recovery

### 2.3 Batch Processing & Quality (Local)
- Support multiple document processing workflows
- Implement quality metrics and validation reporting
- Create human-in-the-loop validation sampling
- Build A/B testing framework for prompt optimization

### 2.4 Migration to Databricks (Version 2)

#### 2.4.1 Environment Setup (Databricks)
- Configure Databricks cluster with ML runtime
- Install libraries: `%pip install pymgclient openai anthropic pydantic`
- Setup Databricks secrets for API keys and Memgraph connection
- Create Delta Lake schemas for entities, relationships, and metadata

#### 2.4.2 Distributed Processing (Databricks)
- Convert Marimo notebooks to Databricks format
- Implement distributed chunking using Spark DataFrames
- Configure parallel LLM processing with resource management
- Add job scheduling and monitoring capabilities

#### 2.4.3 Production Storage (Databricks)
- Replace local storage with Delta Lake tables
- Implement batch Memgraph operations for performance
- Add Delta Lake versioning and time travel
- Create automated backup and lineage tracking

---

## Phase 3: Version 3 - Optimization & Hybrid Approach

### 3.1 NER Integration (Local)
```bash
pip install spacy && python -m spacy download en_core_web_lg
```
- Configure spaCy with domain-specific models
- Create entity type mapping from spaCy labels to ontology
- Implement custom recognition rules and pattern matching
- Add NER visualization and debugging tools

### 3.2 Hybrid Processing Logic (Local)
- Design intelligent decision framework (NER vs. LLM usage)
- Implement cost optimization algorithms and tracking
- Create confidence-based fallback mechanisms
- Add hybrid processing controls in Marimo interface

### 3.3 Advanced Quality & Confidence (Local)
- Combine spaCy confidence with LLM validation scores
- Implement relationship confidence based on context analysis
- Create threshold-based filtering for GraphRAG applications
- Build comprehensive quality benchmarking tools

### 3.4 Production Migration (Databricks)

#### 3.4.1 spaCy Integration (Databricks)
- Configure spaCy models across cluster nodes
- Implement distributed NER using Pandas UDFs
- Optimize model loading and memory management
- Setup model versioning and deployment pipelines

#### 3.4.2 MLOps & Monitoring (Databricks)
- Integrate with MLflow for experiment tracking
- Implement automated quality monitoring and alerting
- Create cost optimization reporting and recommendations
- Design rollback mechanisms for quality degradation

#### 3.4.3 GraphRAG Optimization (Databricks)
- Implement confidence-based graph querying
- Create semantic similarity indexes for hybrid retrieval
- Add graph traversal optimization for common query patterns
- Build GraphRAG performance benchmarking tools

---

## Development Workflow

### Local Development (All Versions):
1. **Marimo Environment**: Interactive development and testing
2. **Sample Data**: Use provided ontology and text examples
3. **Incremental Testing**: Validate each component independently
4. **Quality Metrics**: Built-in accuracy and performance monitoring

### Production Migration (Versions 2-3):
1. **Databricks Deployment**: Distributed processing and storage
2. **Delta Lake Integration**: Versioned, scalable data management
3. **Production Monitoring**: Automated quality and cost tracking
4. **GraphRAG Ready**: Optimized for downstream retrieval applications

---

## Repository Structure

```
├── notebooks/
│   ├── v1_llm_function_calling/
│   ├── v2_chunked_pipeline/
│   └── v3_hybrid_ner_llm/
├── src/
│   ├── ontology/
│   ├── extraction/
│   ├── validation/
│   └── graph_construction/
├── data/
│   ├── samples/
│   └── ontologies/
├── tests/
├── docs/
└── README.md
```

## Getting Started

1. **Clone repository and setup environment**
2. **Start with Version 1**: Follow Phase 1 implementation guide
3. **Test with sample data**: Use provided ontology and text examples
4. **Iterate and improve**: Move to Version 2 for scale, Version 3 for optimization
5. **Deploy to production**: Migrate to Databricks for large-scale processing

## Contributing

- Follow the progressive implementation approach (V1 → V2 → V3)
- Test with sample ontology before customizing
- Maintain backward compatibility for tabular schema
- Document any ontology or schema changes

## Questions & Support

For questions about implementation, ontology design, or GraphRAG integration, please open an issue with:
- Your target ontology complexity
- Expected text types and volume
- Quality vs. cost requirements
- Specific GraphRAG use cases

### **Ontology Schema Documentation**

The system uses a structured ontology with entity types, relationship types, and their descriptions. The ontology defines what can be extracted from text and how entities relate to each other.

#### **Ontology JSON Schema Format:**
```json
{
  "entity_types": {
    "Person": "Individual human being with name and identity",
    "Organization": "Company, institution, group, or formal entity",
    "Location": "Physical place, address, or geographical area",
    "Project": "Specific initiative, task, or organized effort",
    "Event": "Occurrence, meeting, or time-bound activity",
    "Product": "Software, service, or deliverable item",
    "Role": "Job title, position, or functional responsibility"
  },
  "relationship_types": {
    "works_for": "Employment or affiliation relationship",
    "located_at": "Physical or organizational location relationship",
    "manages": "Supervisory or leadership relationship",
    "participates_in": "Involvement in project, event, or activity",
    "reports_to": "Hierarchical reporting relationship",
    "collaborates_with": "Working partnership or cooperation",
    "occurred_at": "Event location or timing relationship",
    "develops": "Creation or development relationship",
    "holds_role": "Position or title assignment relationship"
  }
}
```

#### **Ontology Requirements:**
- **Entity Types**: Must match current `EntityType` enum values exactly
- **Relationship Types**: Must match current `RelationshipType` enum values exactly  
- **Descriptions**: Should provide clear guidance for LLM extraction
- **Consistency**: JSON schema must validate against existing Pydantic models

#### **Updated Build Order with Ontology Schema Step:**

#### **Foundation Layer** (no dependencies):
1. ✅ `process_text_row()` - Row → standardized JSON (**COMPLETE**)
2. ✅ **LLM Configuration** - Proper config files with auto-detection (**COMPLETE**)
   - **Note**: Refactored from `create_llm_config()` function to proper config imports
   - **Usage**: `from config.llm_config import LLM_CONFIGS, get_llm_config`
3. 🔄 `create_triples_dataframe()` - Initialize DataFrame schema (**NEXT**)
4. 🔄 `create_graph_client()` - Graph client initialization

#### **Ontology Schema Alignment** (extract current schema):
5. **Extract entity/relationship definitions** from `src/extraction/llm_extractor.py`
6. **Create `examples/ontologies/default_ontology.json`** with current schema + descriptions
7. **Validate JSON schema** matches current Pydantic models exactly
8. `load_ontology()` - Load ontology JSON from file
9. **PAUSE** - Validate ontology schema alignment before proceeding

#### **LLM Integration** (depends on ontology + config):
10. `extract_triples()` - Main extraction with internal LLM calling
