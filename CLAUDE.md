# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Testing and Development
```bash
# Install dependencies
uv sync

# Test core functionality (no API keys required)
uv run python test_core_functionality.py

# Test with LLM integration (requires API keys in .env)
cd notebooks/v1_llm_function_calling
uv run python test_extraction.py

# Launch interactive Marimo interface
uv run marimo edit notebooks/v1_llm_function_calling/graph_ingestion_v1.py
```

### Environment Setup
```bash
# Copy environment template and configure API keys
cp .env.example .env
# Edit .env with OPENAI_API_KEY or ANTHROPIC_API_KEY

# Optional: Start Memgraph for graph database features
docker run -p 7687:7687 memgraph/memgraph

# Install pymgclient (requires cmake)
brew install cmake  # macOS
uv run pip install pymgclient
```

### Testing Individual Components
```bash
# Test specific functionality
uv run python -c "import sys; sys.path.append('src'); from ontology.models import EntityType; print(list(EntityType))"
uv run python -c "import sys; sys.path.append('src'); from validation.quality_metrics import QualityMetrics; print('Quality metrics available')"
```

## Architecture Overview

### Two-Step Pipeline Architecture
The system implements a **Freeform Text → Tabular → Knowledge Graph** pipeline:

1. **Text Input → LLM Function Calling → Pydantic Validation → 14-Column Tabular Schema**
2. **Tabular Data → Entity Deduplication → Graph Construction → Memgraph Storage**

### Core Components

**Ontology System (`src/ontology/models.py`)**
- Central ontology with 7 EntityTypes (Person, Organization, Location, Project, Event, Product, Role)
- 9 RelationshipTypes (works_for, located_at, manages, etc.)
- 14-column `EntityRelationshipTriple` schema with full metadata
- All models use Pydantic with `use_enum_values = True` configuration

**LLM Integration (`src/extraction/llm_extractor.py`)**
- Dual provider support: OpenAI and Anthropic APIs
- Function calling for structured output extraction
- Automatic retry logic and validation
- Generates unique entity IDs and confidence scores

**Quality Validation (`src/validation/quality_metrics.py`)**
- Comprehensive metrics: confidence scores, entity/relationship distributions, validation issues
- Export capabilities: CSV (14 columns), JSON (full metadata)
- Quality reporting across multiple documents
- Real-time validation and error detection

**Graph Construction (`src/graph_construction/memgraph_client.py`)**
- Memgraph database integration with Cypher queries
- Entity deduplication and relationship management
- Schema setup with proper indexing
- Graph statistics and querying capabilities

### Progressive Implementation Strategy
- **Version 1** (Implemented): LLM + Function Calling for simple texts (<1000 words)
- **Version 2** (Planned): Chunked pipeline for complex documents (1000+ words)
- **Version 3** (Planned): Hybrid NER + LLM for cost optimization

### Interactive Development Environment
The Marimo notebook (`notebooks/v1_llm_function_calling/graph_ingestion_v1.py`) provides:
- Configuration management for API keys
- Real-time text processing and extraction
- Quality metrics visualization
- Export functionality and graph integration

## Key Implementation Details

### Enum Handling
All Pydantic models use `model_config = {"use_enum_values": True}`, meaning enum values are automatically converted to strings. When working with the codebase:
- Access enum values directly: `triple.relationship_type` (not `.value`)
- This affects quality metrics, export functions, and validation logic

### Entity ID Generation
Entity IDs are automatically generated using: `f"{entity_name.lower().replace(' ', '_')}_{uuid.uuid4().hex[:8]}"`

### Testing Strategy
- `test_core_functionality.py`: Tests all functionality without external dependencies
- `test_extraction.py`: Full integration tests requiring API keys
- Marimo interface: Interactive testing and development

### Environment Dependencies
- **Required**: Python 3.13+, uv package manager, API keys for LLM providers
- **Optional**: Docker for Memgraph, cmake for pymgclient installation
- Uses `.env` file for configuration management

### Error Handling Patterns
The codebase uses defensive programming with try/catch blocks and validation at multiple levels:
- Pydantic model validation for data integrity
- LLM response validation and retry logic
- Database connection error handling
- Quality metrics validation for extraction results