// Minimal test ontology for unit tests

// Entity Types
CREATE (:EntityType {
  name: "Person",
  description: "Individual person",
  properties: ["name"]
});

CREATE (:EntityType {
  name: "Organization", 
  description: "Company or institution",
  properties: ["name"]
});

// Relationship Types
CREATE (:RelationshipType {
  name: "works_at",
  description: "Person employed by Organization",
  source_entities: ["Person"],
  target_entities: ["Organization"],
  properties: ["role"]
});

CREATE (:RelationshipType {
  name: "manages",
  description: "Person manages Organization",
  source_entities: ["Person"],
  target_entities: ["Organization"],
  properties: ["since"]
});
