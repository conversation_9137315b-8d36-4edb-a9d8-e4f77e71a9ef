// Default Ontology for Graph Ingestion System
// This ontology defines entity types and relationship types for general document processing

// Entity Type Definitions
CREATE (:EntityType {
  name: "Person",
  description: "Individuals (employees, customers, contacts, leaders)",
  properties: ["name", "email", "phone", "title"]
});

CREATE (:EntityType {
  name: "Organization", 
  description: "Companies, departments, teams, institutions",
  properties: ["name", "industry", "size", "location"]
});

CREATE (:EntityType {
  name: "Location",
  description: "Offices, cities, buildings, regions, countries", 
  properties: ["name", "address", "coordinates", "type"]
});

CREATE (:EntityType {
  name: "Technology",
  description: "Software, hardware, platforms, tools, systems",
  properties: ["name", "version", "vendor", "category"]
});

CREATE (:EntityType {
  name: "Product",
  description: "Services, applications, offerings, solutions",
  properties: ["name", "version", "description", "category"]
});

CREATE (:EntityType {
  name: "Event",
  description: "Meetings, projects, initiatives, launches",
  properties: ["name", "date", "location", "type"]
});

CREATE (:EntityType {
  name: "Role",
  description: "Job titles, positions, responsibilities",
  properties: ["title", "department", "level", "responsibilities"]
});

CREATE (:EntityType {
  name: "Concept",
  description: "Ideas, strategies, methodologies, processes",
  properties: ["name", "description", "category", "domain"]
});

// Relationship Type Definitions with Entity Constraints
CREATE (:RelationshipType {
  name: "works_at",
  description: "Person employed by Organization",
  source_entities: ["Person"],
  target_entities: ["Organization"],
  properties: ["start_date", "role", "department"]
});

CREATE (:RelationshipType {
  name: "located_in",
  description: "Entity physically situated in Location",
  source_entities: ["Person", "Organization", "Event"],
  target_entities: ["Location"],
  properties: ["since_date", "address_type"]
});

CREATE (:RelationshipType {
  name: "leads",
  description: "Person manages/directs Organization/Team",
  source_entities: ["Person"],
  target_entities: ["Organization", "Person", "Event"],
  properties: ["since_date", "scope"]
});

CREATE (:RelationshipType {
  name: "uses",
  description: "Entity utilizes Technology/Product",
  source_entities: ["Person", "Organization"],
  target_entities: ["Technology", "Product"],
  properties: ["since_date", "purpose"]
});

CREATE (:RelationshipType {
  name: "participates_in",
  description: "Entity involved in Event",
  source_entities: ["Person", "Organization"],
  target_entities: ["Event"],
  properties: ["role", "start_date", "end_date"]
});

CREATE (:RelationshipType {
  name: "holds_role",
  description: "Person has specific Role",
  source_entities: ["Person"],
  target_entities: ["Role"],
  properties: ["start_date", "end_date", "status"]
});

CREATE (:RelationshipType {
  name: "part_of",
  description: "Entity belongs to larger Entity",
  source_entities: ["Person", "Organization", "Location"],
  target_entities: ["Organization", "Location"],
  properties: ["since_date", "relationship_type"]
});

CREATE (:RelationshipType {
  name: "collaborates_with",
  description: "Entities work together",
  source_entities: ["Person", "Organization"],
  target_entities: ["Person", "Organization"],
  properties: ["project", "start_date", "end_date"]
});

CREATE (:RelationshipType {
  name: "develops",
  description: "Entity creates/builds Product/Technology",
  source_entities: ["Person", "Organization"],
  target_entities: ["Product", "Technology"],
  properties: ["start_date", "role", "status"]
});

CREATE (:RelationshipType {
  name: "manages",
  description: "Entity oversees/controls other Entity",
  source_entities: ["Person"],
  target_entities: ["Person", "Organization", "Event", "Product"],
  properties: ["since_date", "scope", "authority_level"]
});
