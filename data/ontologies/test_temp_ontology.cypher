// Clear existing data
MATCH (n) DETACH DELETE n;

// Create Core Concepts (renamed Entities to Incidents)
CREATE (incidents:Concept {id: 'incidents', name: 'Incidents', type: 'core'});
CREATE (people:Concept {id: 'people', name: 'People', type: 'core'});
CREATE (resources:Concept {id: 'resources', name: 'Resources', type: 'core'});
CREATE (locations:Concept {id: 'locations', name: 'Locations', type: 'core'});
CREATE (missions:Concept {id: 'missions', name: 'Missions', type: 'core'});

// Create additional entity types (split government agency into state and federal)
CREATE (business:Concept {id: 'business', name: 'Business', type: 'entity_type'});
CREATE (stateAgency:Concept {id: 'state_agency', name: 'State Agency', type: 'entity_type'});
CREATE (federalAgency:Concept {id: 'federal_agency', name: 'Federal Agency', type: 'entity_type'});
CREATE (person:Concept {id: 'person', name: 'Person', type: 'entity_type'});

// Create Concept Hierarchy
MATCH (i:Concept {id: 'incidents'}), 
      (p:Concept {id: 'people'}),
      (r:Concept {id: 'resources'}),
      (l:Concept {id: 'locations'}),
      (m:Concept {id: 'missions'}),
      (bus:Concept {id: 'business'}),
      (state:Concept {id: 'state_agency'}),
      (federal:Concept {id: 'federal_agency'}),
      (pers:Concept {id: 'person'})
CREATE (i)-[:HAS_SUBCLASS]->(p),
       (i)-[:HAS_SUBCLASS]->(r),
       (i)-[:HAS_SUBCLASS]->(l),
       (i)-[:HAS_SUBCLASS]->(m),
       (p)-[:HAS_SUBCLASS]->(pers),
       (i)-[:HAS_SUBCLASS]->(bus),
       (i)-[:HAS_SUBCLASS]->(state),
       (i)-[:HAS_SUBCLASS]->(federal);

// Create Users (People) - removed subrecipient
CREATE (fdemUser:User:Person {id: 'fdem_user', name: 'FDEM User', type: 'staff', status: 'active'});
CREATE (generalPublic:User:Person {id: 'general_public', name: 'General Public', type: 'public', status: 'active'});

// Create Organizations with proper types (updated to use state/federal agency types)
CREATE (fdem:Organization:StateAgency {id: 'fdem', name: 'Florida Division of Emergency Management', type: 'state', status: 'active'});
CREATE (fema:Organization:FederalAgency {id: 'fema', name: 'Federal Emergency Management Agency', type: 'federal', status: 'active'});
CREATE (vendor:Organization:Business {id: 'vendor', name: 'Vendor Company', type: 'vendor', status: 'active'});

// Create Resources
CREATE (equipment:Resource {id: 'equipment', name: 'Equipment', type: 'physical', status: 'available'});
CREATE (supplies:Resource {id: 'supplies', name: 'Supplies', type: 'consumable', status: 'available'});
CREATE (personnel:Resource:Person {id: 'personnel', name: 'Personnel', type: 'human', status: 'available'});

// Create Locations with updated hierarchy (removed Florida, added locality and DEM Region)
CREATE (state:Location {id: 'state', name: 'State', type: 'state', status: 'active'});
CREATE (demRegion:Location {id: 'dem_region', name: 'DEM Region', type: 'region', status: 'active'});
CREATE (county:Location {id: 'county', name: 'County', type: 'county', status: 'active'});
CREATE (locality:Location {id: 'locality', name: 'Locality', type: 'locality', status: 'active'});
CREATE (shelter:Location {id: 'shelter', name: 'Shelter', type: 'facility', status: 'active'});

// Create location hierarchy with LOCATED_IN relationships (boils up structure)
MATCH (dr:Location {id: 'dem_region'}), (s:Location {id: 'state'})
CREATE (dr)-[:LOCATED_IN]->(s);

MATCH (c:Location {id: 'county'}), (dr:Location {id: 'dem_region'})
CREATE (c)-[:LOCATED_IN]->(dr);

MATCH (l:Location {id: 'locality'}), (c:Location {id: 'county'})
CREATE (l)-[:LOCATED_IN]->(c);

MATCH (sh:Location {id: 'shelter'}), (l:Location {id: 'locality'})
CREATE (sh)-[:LOCATED_IN]->(l);

// Create Missions (replaced disaster_response, recovery, mitigation with Fuel and Logistics)
CREATE (fuel:Mission {id: 'fuel', name: 'Fuel', type: 'operational', status: 'active'});
CREATE (logistics:Mission {id: 'logistics', name: 'Logistics', type: 'operational', status: 'active'});

// Connect Users to People concept
MATCH (u:User), (p:Concept {id: 'people'})
CREATE (u)-[:IS_TYPE_OF]->(p);

// Connect Organizations to their entity types
MATCH (o:Organization:Business), (e:Concept {id: 'business'})
CREATE (o)-[:IS_TYPE_OF]->(e);

MATCH (o:Organization:StateAgency), (e:Concept {id: 'state_agency'})
CREATE (o)-[:IS_TYPE_OF]->(e);

MATCH (o:Organization:FederalAgency), (e:Concept {id: 'federal_agency'})
CREATE (o)-[:IS_TYPE_OF]->(e);

// Connect Resources to Resources concept
MATCH (r:Resource), (res:Concept {id: 'resources'})
CREATE (r)-[:IS_TYPE_OF]->(res);

// Connect Personnel to Person concept
MATCH (p:Resource:Person), (pers:Concept {id: 'person'})
CREATE (p)-[:IS_TYPE_OF]->(pers);

// Connect Locations to Locations concept
MATCH (l:Location), (loc:Concept {id: 'locations'})
CREATE (l)-[:IS_TYPE_OF]->(loc);

// Connect Missions to Missions concept
MATCH (m:Mission), (miss:Concept {id: 'missions'})
CREATE (m)-[:IS_TYPE_OF]->(miss);

// Connect Users to Organizations
MATCH (u:User {id: 'fdem_user'}), (o:Organization {id: 'fdem'})
CREATE (u)-[:AFFILIATED_WITH {role: 'staff', since: '2023-01-01'}]->(o);

// Vendor provides resources
MATCH (v:Organization {id: 'vendor'}), 
      (o:Organization {id: 'fdem'}),
      (r:Resource)
CREATE (v)-[:HAS_CONTRACT_WITH {since: '2023-01-01'}]->(o),
       (v)-[:PROVIDES]->(r);

// FDEM partners with FEMA
MATCH (f:Organization {id: 'fdem'}), (fe:Organization {id: 'fema'})
CREATE (f)-[:PARTNERS_WITH {since: '2023-01-01'}]->(fe);

// Connect Missions to Resources and Locations
MATCH (m:Mission), (s:Location {id: 'state'})
CREATE (m)-[:DEPLOYED_TO]->(s);

// Direct relationship from mission to resource
MATCH (m:Mission), (r:Resource)
CREATE (m)-[:REQUIRES]->(r);

// Create Indexes
CREATE INDEX ON :Concept(id);
CREATE INDEX ON :User(id);
CREATE INDEX ON :Organization(id);
CREATE INDEX ON :Resource(id);
CREATE INDEX ON :Location(id);
CREATE INDEX ON :Mission(id);
CREATE INDEX ON :Business(id);
CREATE INDEX ON :StateAgency(id);
CREATE INDEX ON :FederalAgency(id);
CREATE INDEX ON :Person(id);

// Verify the Graph Structure
MATCH (n)
WITH count(n) as nodeCount
MATCH ()-[r]->()
RETURN 'Ontology setup complete. Graph contains ' + 
       nodeCount + ' nodes and ' +
       count(r) + ' relationships.' AS status;