[{"document_id": "UID_1", "extraction_timestamp": "2025-07-08T22:55:19.428343", "total_triples": 3, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "holds_role", "entity_object": "VP of Engineering", "entity_subject_type": "Person", "entity_object_type": "Role", "confidence_score": 0.85, "source_text_snippet": "<PERSON>, the VP of Engineering", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Role assignment", "extraction_timestamp": "2025-07-08T22:55:19.428315", "entity_subject_id": "sarah_johnson_8eef579c", "entity_object_id": "vp_engineering_b61f0c92"}, {"entity_subject": "<PERSON>", "relationship_type": "works_for", "entity_object": "TechCorp", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "VP of Engineering at TechCorp", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Employment relationship", "extraction_timestamp": "2025-07-08T22:55:19.428330", "entity_subject_id": "sarah_johnson_eeb61ea2", "entity_object_id": "techcorp_1e050fc0"}, {"entity_subject": "TechCorp", "relationship_type": "collaborates_with", "entity_object": "DataSoft", "entity_subject_type": "Organization", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "in collaboration with DataSoft", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Business partnership", "extraction_timestamp": "2025-07-08T22:55:19.428337", "entity_subject_id": "techcorp_d902e870", "entity_object_id": "datasoft_20e61346"}]}, {"document_id": "UID_2", "extraction_timestamp": "2025-07-08T22:55:19.428384", "total_triples": 1, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "develops", "entity_object": "customer analytics platform", "entity_subject_type": "Person", "entity_object_type": "Product", "confidence_score": 0.85, "source_text_snippet": "<PERSON> presented the new customer analytics platform", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Product development", "extraction_timestamp": "2025-07-08T22:55:19.428381", "entity_subject_id": "mike_chen_6838262c", "entity_object_id": "analytics_platform_22014caf"}]}, {"document_id": "UID_3", "extraction_timestamp": "2025-07-08T22:55:19.428406", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "extraction_timestamp": "2025-07-08T22:55:19.428404", "entity_subject_id": "sample_entity_2e851061", "entity_object_id": "sample_org_5b14b598"}]}, {"document_id": "UID_4", "extraction_timestamp": "2025-07-08T22:55:19.428425", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "extraction_timestamp": "2025-07-08T22:55:19.428424", "entity_subject_id": "sample_entity_00c6fa8a", "entity_object_id": "sample_org_26110330"}]}, {"document_id": "UID_5", "extraction_timestamp": "2025-07-08T22:55:19.428442", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "extraction_timestamp": "2025-07-08T22:55:19.428441", "entity_subject_id": "sample_entity_ca992b4b", "entity_object_id": "sample_org_a24a0c23"}]}]