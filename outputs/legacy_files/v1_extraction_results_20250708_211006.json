[{"document_id": "UID_1", "extraction_timestamp": "2025-07-08T21:10:06.395207", "total_triples": 3, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "holds_role", "entity_object": "VP of Engineering", "entity_subject_type": "Person", "entity_object_type": "Role", "confidence_score": 0.85, "source_text_snippet": "<PERSON>, the VP of Engineering", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Role assignment", "entity_subject_id": "sarah_johnson_60c34ad5", "entity_object_id": "vp_engineering_e9b34999"}, {"entity_subject": "<PERSON>", "relationship_type": "works_for", "entity_object": "TechCorp", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "VP of Engineering at TechCorp", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Employment relationship", "entity_subject_id": "sarah_johnson_c43143f2", "entity_object_id": "techcorp_43fcd377"}, {"entity_subject": "TechCorp", "relationship_type": "collaborates_with", "entity_object": "DataSoft", "entity_subject_type": "Organization", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "in collaboration with DataSoft", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Business partnership", "entity_subject_id": "techcorp_8266d186", "entity_object_id": "datasoft_1b9f944b"}]}, {"document_id": "UID_2", "extraction_timestamp": "2025-07-08T21:10:06.395242", "total_triples": 1, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "develops", "entity_object": "customer analytics platform", "entity_subject_type": "Person", "entity_object_type": "Product", "confidence_score": 0.85, "source_text_snippet": "<PERSON> presented the new customer analytics platform", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Product development", "entity_subject_id": "mike_chen_4e50d2a7", "entity_object_id": "analytics_platform_19a3d3e5"}]}, {"document_id": "UID_3", "extraction_timestamp": "2025-07-08T21:10:06.395253", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_78dafb5a", "entity_object_id": "sample_org_b78bf50c"}]}, {"document_id": "UID_4", "extraction_timestamp": "2025-07-08T21:10:06.395263", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_498690ac", "entity_object_id": "sample_org_3a399aee"}]}, {"document_id": "UID_5", "extraction_timestamp": "2025-07-08T21:10:06.395272", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_b525e56b", "entity_object_id": "sample_org_c6903238"}]}]