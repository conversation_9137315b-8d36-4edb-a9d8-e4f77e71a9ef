[{"document_id": "UID_1", "extraction_timestamp": "2025-07-08T21:10:13.664882", "total_triples": 3, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "holds_role", "entity_object": "VP of Engineering", "entity_subject_type": "Person", "entity_object_type": "Role", "confidence_score": 0.85, "source_text_snippet": "<PERSON>, the VP of Engineering", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Role assignment", "entity_subject_id": "sarah_johnson_0efa0371", "entity_object_id": "vp_engineering_43d039dc"}, {"entity_subject": "<PERSON>", "relationship_type": "works_for", "entity_object": "TechCorp", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "VP of Engineering at TechCorp", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Employment relationship", "entity_subject_id": "sarah_johnson_b65b2822", "entity_object_id": "techcorp_508d06bd"}, {"entity_subject": "TechCorp", "relationship_type": "collaborates_with", "entity_object": "DataSoft", "entity_subject_type": "Organization", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "in collaboration with DataSoft", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Business partnership", "entity_subject_id": "techcorp_3b203940", "entity_object_id": "datasoft_a880e894"}]}, {"document_id": "UID_2", "extraction_timestamp": "2025-07-08T21:10:13.664902", "total_triples": 1, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "develops", "entity_object": "customer analytics platform", "entity_subject_type": "Person", "entity_object_type": "Product", "confidence_score": 0.85, "source_text_snippet": "<PERSON> presented the new customer analytics platform", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Product development", "entity_subject_id": "mike_chen_cf774ac0", "entity_object_id": "analytics_platform_5452bcee"}]}, {"document_id": "UID_3", "extraction_timestamp": "2025-07-08T21:10:13.664913", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_1a607288", "entity_object_id": "sample_org_76758901"}]}, {"document_id": "UID_4", "extraction_timestamp": "2025-07-08T21:10:13.664922", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_e882e98f", "entity_object_id": "sample_org_e88c123c"}]}, {"document_id": "UID_5", "extraction_timestamp": "2025-07-08T21:10:13.664930", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_9da63ab8", "entity_object_id": "sample_org_bf9b2635"}]}]