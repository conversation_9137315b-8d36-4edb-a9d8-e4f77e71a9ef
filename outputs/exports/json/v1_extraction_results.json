[{"document_id": "UID_1", "extraction_timestamp": "2025-07-08T22:47:16.053284", "total_triples": 3, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "holds_role", "entity_object": "VP of Engineering", "entity_subject_type": "Person", "entity_object_type": "Role", "confidence_score": 0.85, "source_text_snippet": "<PERSON>, the VP of Engineering", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Role assignment", "entity_subject_id": "sarah_johnson_4ab72402", "entity_object_id": "vp_engineering_70cfa162"}, {"entity_subject": "<PERSON>", "relationship_type": "works_for", "entity_object": "TechCorp", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "VP of Engineering at TechCorp", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Employment relationship", "entity_subject_id": "sarah_johnson_8a794ca2", "entity_object_id": "techcorp_7704061a"}, {"entity_subject": "TechCorp", "relationship_type": "collaborates_with", "entity_object": "DataSoft", "entity_subject_type": "Organization", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "in collaboration with DataSoft", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Business partnership", "entity_subject_id": "techcorp_37c3d900", "entity_object_id": "datasoft_d9873799"}]}, {"document_id": "UID_2", "extraction_timestamp": "2025-07-08T22:47:16.053303", "total_triples": 1, "triples": [{"entity_subject": "<PERSON>", "relationship_type": "develops", "entity_object": "customer analytics platform", "entity_subject_type": "Person", "entity_object_type": "Product", "confidence_score": 0.85, "source_text_snippet": "<PERSON> presented the new customer analytics platform", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "Product development", "entity_subject_id": "mike_chen_ee8b06ce", "entity_object_id": "analytics_platform_282453da"}]}, {"document_id": "UID_3", "extraction_timestamp": "2025-07-08T22:47:16.053313", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_76a11763", "entity_object_id": "sample_org_1f92fc35"}]}, {"document_id": "UID_4", "extraction_timestamp": "2025-07-08T22:47:16.053323", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_0f9ad01f", "entity_object_id": "sample_org_eb2caa55"}]}, {"document_id": "UID_5", "extraction_timestamp": "2025-07-08T22:47:16.053331", "total_triples": 1, "triples": [{"entity_subject": "Sample Entity", "relationship_type": "works_for", "entity_object": "Sample Organization", "entity_subject_type": "Person", "entity_object_type": "Organization", "confidence_score": 0.85, "source_text_snippet": "Sample text snippet", "extraction_method": "llm_function_calling", "chunk_id": "full_text", "relationship_context": "<PERSON><PERSON> relationship", "entity_subject_id": "sample_entity_0b0edc75", "entity_object_id": "sample_org_36ba30dc"}]}]