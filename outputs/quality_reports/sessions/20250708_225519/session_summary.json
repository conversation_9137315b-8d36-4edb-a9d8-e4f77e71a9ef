{"session_info": {"session_id": "20250708_225519", "start_time": "20250708_225519", "end_time": "20250708_225519", "project_root": "/Users/<USER>/Repos/graph_ingestion"}, "processing_summary": {"documents_processed": 5, "total_triples_extracted": 7, "unique_entities_identified": 8, "avg_triples_per_document": 1.4}, "files_generated": {"extraction_results": "/Users/<USER>/Repos/graph_ingestion/outputs/extractions/v1/v1_extraction_results_20250708_225519.json", "quality_report": "/Users/<USER>/Repos/graph_ingestion/outputs/quality_reports/v1/v1_quality_report_20250708_225519.json", "csv": "/Users/<USER>/Repos/graph_ingestion/outputs/exports/csv/v1_extraction_results.csv", "json": "/Users/<USER>/Repos/graph_ingestion/outputs/exports/json/v1_extraction_results.json"}, "quality_metrics": {"total_documents": 5, "total_triples": 7, "avg_triples_per_doc": 1.4, "avg_confidence": 0.85, "avg_entities_per_doc": 2.4, "success_rate": 1.0, "total_issues": 0, "total_warnings": 0}, "next_steps": ["Review exported CSV/JSON files", "Analyze quality metrics for insights", "Experiment with custom text inputs", "Consider scaling to Version 2 for larger documents"]}