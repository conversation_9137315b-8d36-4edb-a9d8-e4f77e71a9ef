{"summary": {"total_documents": 5, "total_triples": 7, "avg_triples_per_doc": 1.4, "avg_confidence": 0.85, "avg_entities_per_doc": 2.4, "success_rate": 1.0, "total_issues": 0, "total_warnings": 0}, "distributions": {"entity_types": {"Person": 6, "Role": 1, "Organization": 6, "Product": 1}, "relationship_types": {"holds_role": 1, "works_for": 4, "collaborates_with": 1, "develops": 1}}, "detailed_metrics": [{"total_triples": 3, "avg_confidence": 0.85, "entity_types_coverage": {"Person": 2, "Role": 1, "Organization": 3}, "relationship_types_coverage": {"holds_role": 1, "works_for": 1, "collaborates_with": 1}, "avg_snippet_length": 31.666666666666668, "extraction_timestamp": "2025-07-08T22:55:19.428343", "document_id": "UID_1", "unique_entities": 4, "unique_subjects": 2, "unique_objects": 3, "avg_connections_per_entity": 1.5, "most_connected_entities": [["<PERSON>", 2], ["TechCorp", 2], ["VP of Engineering", 1], ["DataSoft", 1]], "entity_connections": {"Sarah Johnson": 2, "VP of Engineering": 1, "TechCorp": 2, "DataSoft": 1}, "validation": {"is_valid": true, "issues": [], "warnings": [], "total_issues": 0, "total_warnings": 0}}, {"total_triples": 1, "avg_confidence": 0.85, "entity_types_coverage": {"Person": 1, "Product": 1}, "relationship_types_coverage": {"develops": 1}, "avg_snippet_length": 55.0, "extraction_timestamp": "2025-07-08T22:55:19.428384", "document_id": "UID_2", "unique_entities": 2, "unique_subjects": 1, "unique_objects": 1, "avg_connections_per_entity": 1.0, "most_connected_entities": [["<PERSON>", 1], ["customer analytics platform", 1]], "entity_connections": {"Mike Chen": 1, "customer analytics platform": 1}, "validation": {"is_valid": true, "issues": [], "warnings": [], "total_issues": 0, "total_warnings": 0}}, {"total_triples": 1, "avg_confidence": 0.85, "entity_types_coverage": {"Person": 1, "Organization": 1}, "relationship_types_coverage": {"works_for": 1}, "avg_snippet_length": 19.0, "extraction_timestamp": "2025-07-08T22:55:19.428406", "document_id": "UID_3", "unique_entities": 2, "unique_subjects": 1, "unique_objects": 1, "avg_connections_per_entity": 1.0, "most_connected_entities": [["Sample Entity", 1], ["Sample Organization", 1]], "entity_connections": {"Sample Entity": 1, "Sample Organization": 1}, "validation": {"is_valid": true, "issues": [], "warnings": [], "total_issues": 0, "total_warnings": 0}}, {"total_triples": 1, "avg_confidence": 0.85, "entity_types_coverage": {"Person": 1, "Organization": 1}, "relationship_types_coverage": {"works_for": 1}, "avg_snippet_length": 19.0, "extraction_timestamp": "2025-07-08T22:55:19.428425", "document_id": "UID_4", "unique_entities": 2, "unique_subjects": 1, "unique_objects": 1, "avg_connections_per_entity": 1.0, "most_connected_entities": [["Sample Entity", 1], ["Sample Organization", 1]], "entity_connections": {"Sample Entity": 1, "Sample Organization": 1}, "validation": {"is_valid": true, "issues": [], "warnings": [], "total_issues": 0, "total_warnings": 0}}, {"total_triples": 1, "avg_confidence": 0.85, "entity_types_coverage": {"Person": 1, "Organization": 1}, "relationship_types_coverage": {"works_for": 1}, "avg_snippet_length": 19.0, "extraction_timestamp": "2025-07-08T22:55:19.428442", "document_id": "UID_5", "unique_entities": 2, "unique_subjects": 1, "unique_objects": 1, "avg_connections_per_entity": 1.0, "most_connected_entities": [["Sample Entity", 1], ["Sample Organization", 1]], "entity_connections": {"Sample Entity": 1, "Sample Organization": 1}, "validation": {"is_valid": true, "issues": [], "warnings": [], "total_issues": 0, "total_warnings": 0}}], "generated_at": "2025-07-08T22:55:19.428600", "metadata": {"session_id": "20250708_225519", "version": "v1", "generated_at": "2025-07-08T22:55:19.429014", "project_root": "/Users/<USER>/Repos/graph_ingestion"}}