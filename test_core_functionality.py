#!/usr/bin/env python3
"""
Test core functionality without external dependencies
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.append("src")

def test_export_functionality():
    """Test CSV/JSON export without external dependencies"""
    print("💾 Testing Export Functionality...")
    
    try:
        from validation.quality_metrics import QualityMetrics
        from ontology.models import EntityRelationshipTriple, ExtractionResult, EntityType, RelationshipType
        
        # Create sample triples
        triples = [
            EntityRelationshipTriple(
                entity_subject="<PERSON>",
                relationship_type=RelationshipType.WORKS_FOR,
                entity_object="TechCorp",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.ORGANIZATION,
                source_text_snippet="<PERSON> works at TechCorp",
                relationship_context="Employment relationship",
                document_id="TEST_001",
                entity_subject_id="sarah_johnson_001",
                entity_object_id="techcorp_001"
            ),
            EntityRelationshipTriple(
                entity_subject="<PERSON>",
                relationship_type=RelationshipType.HOLDS_ROLE,
                entity_object="VP of Engineering",
                entity_subject_type=EntityType.PERSON,
                entity_object_type=EntityType.ROLE,
                source_text_snippet="<PERSON>, VP of Engineering",
                relationship_context="Role assignment",
                document_id="TEST_001",
                entity_subject_id="sarah_johnson_001",
                entity_object_id="vp_engineering_001"
            ),
            EntityRelationshipTriple(
                entity_subject="TechCorp",
                relationship_type=RelationshipType.COLLABORATES_WITH,
                entity_object="DataSoft",
                entity_subject_type=EntityType.ORGANIZATION,
                entity_object_type=EntityType.ORGANIZATION,
                source_text_snippet="TechCorp in collaboration with DataSoft",
                relationship_context="Business partnership",
                document_id="TEST_001",
                entity_subject_id="techcorp_001",
                entity_object_id="datasoft_001"
            )
        ]
        
        # Create extraction result
        result = ExtractionResult(
            triples=triples,
            document_id="TEST_001",
            total_triples=len(triples)
        )
        
        quality_calc = QualityMetrics()
        
        # Test CSV export
        csv_file = quality_calc.export_to_csv([result], "test_export.csv")
        print(f"✅ CSV export successful: {csv_file}")
        
        # Test JSON export
        json_file = quality_calc.export_to_json([result], "test_export.json")
        print(f"✅ JSON export successful: {json_file}")
        
        # Verify files exist and have content
        csv_exists = Path(csv_file).exists()
        json_exists = Path(json_file).exists()
        
        if csv_exists:
            csv_size = Path(csv_file).stat().st_size
            print(f"   - CSV file size: {csv_size} bytes")
        
        if json_exists:
            json_size = Path(json_file).stat().st_size
            print(f"   - JSON file size: {json_size} bytes")
        
        # Test quality report
        quality_report = quality_calc.generate_quality_report([result])
        print(f"✅ Quality report generated:")
        print(f"   - Total triples: {quality_report['summary']['total_triples']}")
        print(f"   - Success rate: {quality_report['summary']['success_rate']}")
        print(f"   - Entity types: {list(quality_report['distributions']['entity_types'].keys())}")
        print(f"   - Relationship types: {list(quality_report['distributions']['relationship_types'].keys())}")
        
        return csv_exists and json_exists
        
    except Exception as e:
        print(f"❌ Export test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_full_pipeline_simulation():
    """Test the full pipeline without LLM calls"""
    print("\n🔄 Testing Full Pipeline Simulation...")
    
    try:
        from ontology.models import EntityRelationshipTriple, ExtractionResult, EntityType, RelationshipType
        from validation.quality_metrics import QualityMetrics
        
        # Simulate processing sample data
        sample_texts = [
            {
                "document_id": "UID_1",
                "text": "Sarah Johnson, the VP of Engineering at TechCorp, is leading the AI initiative project in collaboration with DataSoft. The project kicked off last month at the San Francisco headquarters."
            },
            {
                "document_id": "UID_2",
                "text": "The quarterly review meeting was held on March 15th where Mike Chen presented the new customer analytics platform. The platform was developed by his team in partnership with external consultants from InnovateAI."
            }
        ]
        
        # Simulate extraction results for each sample
        all_results = []
        
        for sample in sample_texts:
            # Simulate what the LLM would extract
            if sample["document_id"] == "UID_1":
                triples = [
                    EntityRelationshipTriple(
                        entity_subject="Sarah Johnson",
                        relationship_type=RelationshipType.HOLDS_ROLE,
                        entity_object="VP of Engineering",
                        entity_subject_type=EntityType.PERSON,
                        entity_object_type=EntityType.ROLE,
                        source_text_snippet="Sarah Johnson, the VP of Engineering",
                        relationship_context="Role assignment",
                        document_id=sample["document_id"],
                        entity_subject_id="sarah_johnson_001",
                        entity_object_id="vp_engineering_001"
                    ),
                    EntityRelationshipTriple(
                        entity_subject="Sarah Johnson",
                        relationship_type=RelationshipType.WORKS_FOR,
                        entity_object="TechCorp",
                        entity_subject_type=EntityType.PERSON,
                        entity_object_type=EntityType.ORGANIZATION,
                        source_text_snippet="VP of Engineering at TechCorp",
                        relationship_context="Employment relationship",
                        document_id=sample["document_id"],
                        entity_subject_id="sarah_johnson_001",
                        entity_object_id="techcorp_001"
                    ),
                    EntityRelationshipTriple(
                        entity_subject="AI initiative project",
                        relationship_type=RelationshipType.OCCURRED_AT,
                        entity_object="San Francisco headquarters",
                        entity_subject_type=EntityType.PROJECT,
                        entity_object_type=EntityType.LOCATION,
                        source_text_snippet="kicked off last month at the San Francisco headquarters",
                        relationship_context="Project location",
                        document_id=sample["document_id"],
                        entity_subject_id="ai_initiative_001",
                        entity_object_id="sf_headquarters_001"
                    )
                ]
            else:  # UID_2
                triples = [
                    EntityRelationshipTriple(
                        entity_subject="Mike Chen",
                        relationship_type=RelationshipType.DEVELOPS,
                        entity_object="customer analytics platform",
                        entity_subject_type=EntityType.PERSON,
                        entity_object_type=EntityType.PRODUCT,
                        source_text_snippet="Mike Chen presented the new customer analytics platform",
                        relationship_context="Product development",
                        document_id=sample["document_id"],
                        entity_subject_id="mike_chen_001",
                        entity_object_id="analytics_platform_001"
                    )
                ]
            
            result = ExtractionResult(
                triples=triples,
                document_id=sample["document_id"],
                total_triples=len(triples)
            )
            all_results.append(result)
        
        # Generate comprehensive quality report
        quality_calc = QualityMetrics()
        quality_report = quality_calc.generate_quality_report(all_results)
        
        print(f"✅ Pipeline simulation successful:")
        print(f"   - Documents processed: {quality_report['summary']['total_documents']}")
        print(f"   - Total triples extracted: {quality_report['summary']['total_triples']}")
        print(f"   - Average triples per document: {quality_report['summary']['avg_triples_per_doc']}")
        print(f"   - Success rate: {quality_report['summary']['success_rate']}")
        print(f"   - Entity types found: {list(quality_report['distributions']['entity_types'].keys())}")
        print(f"   - Relationship types found: {list(quality_report['distributions']['relationship_types'].keys())}")
        
        # Export results
        csv_file = quality_calc.export_to_csv(all_results, "pipeline_test_results.csv")
        json_file = quality_calc.export_to_json(all_results, "pipeline_test_results.json")
        
        print(f"✅ Results exported:")
        print(f"   - CSV: {csv_file}")
        print(f"   - JSON: {json_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline simulation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run core functionality tests"""
    print("🚀 Testing Core Functionality (Without External Dependencies)")
    print("=" * 70)
    
    tests = [
        ("Export Functionality", test_export_functionality),
        ("Full Pipeline Simulation", test_full_pipeline_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 Core Functionality Test Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"   - {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🏆 Overall: {passed}/{len(results)} core tests passed")
    
    if passed == len(results):
        print("🎉 All core functionality tests passed!")
        print("\n📋 What's working:")
        print("   ✅ Ontology models and validation")
        print("   ✅ Quality metrics calculation")
        print("   ✅ CSV/JSON export functionality")
        print("   ✅ End-to-end pipeline simulation")
        print("   ✅ Sample data loading")
        print("\n⚠️  What needs external setup:")
        print("   🔗 Memgraph connection (requires docker + pymgclient)")
        print("   🤖 LLM integration (requires API keys)")
        print("\n🚀 Ready for Version 1 testing with API keys!")
    else:
        print("⚠️  Some core tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()