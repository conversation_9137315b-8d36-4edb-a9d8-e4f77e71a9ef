"""
LLM-based entity and relationship extraction using function calling
Version 1 implementation with OpenAI and Anthropic support
"""

import json
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid

import openai
import anthropic
from pydantic import BaseModel

from ontology.models import (
    EntityRelationshipTriple, 
    ExtractionResult, 
    EntityType, 
    RelationshipType
)


class LLMExtractor:
    """
    LLM-based extractor using function calling for structured output
    """
    
    def __init__(self, provider: str = "openai", model: str = None):
        """
        Initialize LLM extractor
        
        Args:
            provider: "openai" or "anthropic"
            model: Specific model to use (optional)
        """
        self.provider = provider
        
        if provider == "openai":
            self.client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            self.model = model or "gpt-4o-mini"
        elif provider == "anthropic":
            self.client = anthropic.Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
            self.model = model or "claude-3-5-sonnet-20241022"
        else:
            raise ValueError(f"Unsupported provider: {provider}")
    
    def _build_ontology_description(self) -> str:
        """Build ontology description for the prompt"""
        entity_types = "\n".join([f"- {e.value}: {self._get_entity_description(e)}" for e in EntityType])
        relationship_types = "\n".join([f"- {r.value}: {self._get_relationship_description(r)}" for r in RelationshipType])
        
        return f"""
ONTOLOGY DEFINITION:

Entity Types:
{entity_types}

Relationship Types:
{relationship_types}

EXAMPLES:
- "Sarah Johnson, VP of Engineering at TechCorp" → 
  Triple 1: Sarah Johnson | holds_role | VP of Engineering | Person | Role
  Triple 2: Sarah Johnson | works_for | TechCorp | Person | Organization
"""
    
    def _get_entity_description(self, entity_type: EntityType) -> str:
        """Get description for entity type"""
        descriptions = {
            EntityType.PERSON: "Individuals (employees, customers, contacts)",
            EntityType.ORGANIZATION: "Companies, departments, teams",
            EntityType.LOCATION: "Offices, cities, buildings, regions",
            EntityType.PROJECT: "Initiatives, programs, research projects",
            EntityType.EVENT: "Meetings, launches, incidents, conferences",
            EntityType.PRODUCT: "Software, platforms, services, tools",
            EntityType.ROLE: "Job titles, positions, responsibilities"
        }
        return descriptions.get(entity_type, "")
    
    def _get_relationship_description(self, rel_type: RelationshipType) -> str:
        """Get description for relationship type"""
        descriptions = {
            RelationshipType.WORKS_FOR: "Person → Organization",
            RelationshipType.LOCATED_AT: "Person/Organization → Location",
            RelationshipType.MANAGES: "Person → Project/Person",
            RelationshipType.PARTICIPATES_IN: "Person → Event/Project",
            RelationshipType.REPORTS_TO: "Person → Person",
            RelationshipType.COLLABORATES_WITH: "Person/Organization ↔ Person/Organization",
            RelationshipType.OCCURRED_AT: "Event → Location",
            RelationshipType.DEVELOPS: "Person/Organization → Product",
            RelationshipType.HOLDS_ROLE: "Person → Role"
        }
        return descriptions.get(rel_type, "")
    
    def _create_function_schema(self) -> Dict[str, Any]:
        """Create function calling schema for OpenAI"""
        return {
            "name": "extract_entities_relationships",
            "description": "Extract entities and relationships from text according to predefined ontology",
            "parameters": {
                "type": "object",
                "properties": {
                    "triples": {
                        "type": "array",
                        "description": "List of entity-relationship triples",
                        "items": {
                            "type": "object",
                            "properties": {
                                "entity_subject": {"type": "string", "description": "Source entity name"},
                                "relationship_type": {
                                    "type": "string", 
                                    "enum": [r.value for r in RelationshipType],
                                    "description": "Relationship type from ontology"
                                },
                                "entity_object": {"type": "string", "description": "Target entity name"},
                                "entity_subject_type": {
                                    "type": "string",
                                    "enum": [e.value for e in EntityType],
                                    "description": "Source entity type"
                                },
                                "entity_object_type": {
                                    "type": "string",
                                    "enum": [e.value for e in EntityType],
                                    "description": "Target entity type"
                                },
                                "source_text_snippet": {"type": "string", "description": "Supporting text from original"},
                                "relationship_context": {"type": "string", "description": "Semantic context of relationship"}
                            },
                            "required": ["entity_subject", "relationship_type", "entity_object", 
                                       "entity_subject_type", "entity_object_type", "source_text_snippet", "relationship_context"]
                        }
                    }
                },
                "required": ["triples"]
            }
        }
    
    def _create_anthropic_tool(self) -> Dict[str, Any]:
        """Create tool schema for Anthropic"""
        return {
            "name": "extract_entities_relationships",
            "description": "Extract entities and relationships from text according to predefined ontology",
            "input_schema": {
                "type": "object",
                "properties": {
                    "triples": {
                        "type": "array",
                        "description": "List of entity-relationship triples",
                        "items": {
                            "type": "object",
                            "properties": {
                                "entity_subject": {"type": "string", "description": "Source entity name"},
                                "relationship_type": {
                                    "type": "string", 
                                    "enum": [r.value for r in RelationshipType],
                                    "description": "Relationship type from ontology"
                                },
                                "entity_object": {"type": "string", "description": "Target entity name"},
                                "entity_subject_type": {
                                    "type": "string",
                                    "enum": [e.value for e in EntityType],
                                    "description": "Source entity type"
                                },
                                "entity_object_type": {
                                    "type": "string",
                                    "enum": [e.value for e in EntityType],
                                    "description": "Target entity type"
                                },
                                "source_text_snippet": {"type": "string", "description": "Supporting text from original"},
                                "relationship_context": {"type": "string", "description": "Semantic context of relationship"}
                            },
                            "required": ["entity_subject", "relationship_type", "entity_object", 
                                       "entity_subject_type", "entity_object_type", "source_text_snippet", "relationship_context"]
                        }
                    }
                },
                "required": ["triples"]
            }
        }
    
    def extract(self, text: str, document_id: str) -> ExtractionResult:
        """
        Extract entities and relationships from text
        
        Args:
            text: Input text to process
            document_id: Unique identifier for the document
            
        Returns:
            ExtractionResult with extracted triples
        """
        prompt = f"""
{self._build_ontology_description()}

TASK: Extract all entities and relationships from the following text according to the ontology above.

TEXT TO ANALYZE:
{text}

INSTRUCTIONS:
1. Identify all entities that match the ontology types
2. Find relationships between entities according to the defined relationship types
3. Extract supporting text snippets that justify each relationship
4. Provide semantic context for each relationship
5. Be precise and only extract relationships that are explicitly stated or strongly implied

Call the extract_entities_relationships function with your results.
"""
        
        if self.provider == "openai":
            return self._extract_openai(prompt, text, document_id)
        else:
            return self._extract_anthropic(prompt, text, document_id)
    
    def _extract_openai(self, prompt: str, text: str, document_id: str) -> ExtractionResult:
        """Extract using OpenAI function calling"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                functions=[self._create_function_schema()],
                function_call={"name": "extract_entities_relationships"},
                temperature=0.1
            )
            
            function_call = response.choices[0].message.function_call
            if function_call and function_call.name == "extract_entities_relationships":
                args = json.loads(function_call.arguments)
                return self._process_extraction_result(args, document_id)
            else:
                raise ValueError("LLM did not call the extraction function")
                
        except Exception as e:
            raise RuntimeError(f"OpenAI extraction failed: {str(e)}")
    
    def _extract_anthropic(self, prompt: str, text: str, document_id: str) -> ExtractionResult:
        """Extract using Anthropic tool calling"""
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=4000,
                temperature=0.1,
                messages=[{"role": "user", "content": prompt}],
                tools=[self._create_anthropic_tool()],
                tool_choice={"type": "tool", "name": "extract_entities_relationships"}
            )
            
            if response.content and response.content[0].type == "tool_use":
                tool_use = response.content[0]
                if tool_use.name == "extract_entities_relationships":
                    return self._process_extraction_result(tool_use.input, document_id)
            
            raise ValueError("Anthropic did not use the extraction tool")
            
        except Exception as e:
            raise RuntimeError(f"Anthropic extraction failed: {str(e)}")
    
    def _process_extraction_result(self, raw_result: Dict[str, Any], document_id: str) -> ExtractionResult:
        """Process raw LLM result into structured format"""
        triples = []
        
        for triple_data in raw_result.get("triples", []):
            # Generate unique IDs for entities
            subject_id = f"{triple_data['entity_subject'].lower().replace(' ', '_')}_{uuid.uuid4().hex[:8]}"
            object_id = f"{triple_data['entity_object'].lower().replace(' ', '_')}_{uuid.uuid4().hex[:8]}"
            
            triple = EntityRelationshipTriple(
                entity_subject=triple_data["entity_subject"],
                relationship_type=triple_data["relationship_type"],
                entity_object=triple_data["entity_object"],
                entity_subject_type=triple_data["entity_subject_type"],
                entity_object_type=triple_data["entity_object_type"],
                source_text_snippet=triple_data["source_text_snippet"],
                relationship_context=triple_data["relationship_context"],
                document_id=document_id,
                entity_subject_id=subject_id,
                entity_object_id=object_id
            )
            triples.append(triple)
        
        return ExtractionResult(
            triples=triples,
            document_id=document_id,
            total_triples=len(triples)
        )