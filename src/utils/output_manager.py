"""
Output Management System
========================

Centralized management for organizing extraction results, quality reports, and exports.
Provides consistent file naming, directory structure, and cleanup utilities.
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import shutil

from ontology.models import ExtractionResult


class OutputManager:
    """
    Manages organized storage of extraction results, quality reports, and exports.
    
    Directory Structure:
    outputs/
    ├── extractions/           # Raw extraction results
    │   ├── v1/               # Version 1 results
    │   ├── v2/               # Version 2 results (future)
    │   └── v3/               # Version 3 results (future)
    ├── quality_reports/       # Quality analysis reports
    │   ├── v1/
    │   └── sessions/         # Individual session reports
    └── exports/              # Final exports (CSV, JSON, etc.)
        ├── csv/
        ├── json/
        └── archives/         # Compressed archives
    """
    
    def __init__(self, project_root: Optional[Path] = None):
        """
        Initialize output manager
        
        Args:
            project_root: Project root directory (auto-detected if None)
        """
        if project_root is None:
            # Auto-detect project root (look for pyproject.toml)
            current = Path(__file__).parent
            while current != current.parent:
                if (current / "pyproject.toml").exists():
                    project_root = current
                    break
                current = current.parent
            else:
                project_root = Path.cwd()
        
        self.project_root = Path(project_root)
        self.outputs_dir = self.project_root / "outputs"
        
        # Directory structure
        self.extractions_dir = self.outputs_dir / "extractions"
        self.quality_dir = self.outputs_dir / "quality_reports" 
        self.exports_dir = self.outputs_dir / "exports"
        
        # Version-specific directories
        self.v1_extractions = self.extractions_dir / "v1"
        self.v1_quality = self.quality_dir / "v1"
        
        # Export type directories
        self.csv_exports = self.exports_dir / "csv"
        self.json_exports = self.exports_dir / "json"
        self.archives_dir = self.exports_dir / "archives"
        
        # Session tracking
        self.session_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.session_dir = self.quality_dir / "sessions" / self.session_id
        
        # Initialize directories
        self._setup_directories()
    
    def _setup_directories(self):
        """Create directory structure if it doesn't exist"""
        directories = [
            self.extractions_dir,
            self.quality_dir,
            self.exports_dir,
            self.v1_extractions,
            self.v1_quality,
            self.csv_exports,
            self.json_exports,
            self.archives_dir,
            self.session_dir,
            self.quality_dir / "sessions"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Create .gitkeep files to preserve empty directories
        for directory in directories:
            gitkeep = directory / ".gitkeep"
            if not gitkeep.exists() and not any(directory.iterdir()):
                gitkeep.touch()
    
    def generate_filename(self, base_name: str, extension: str, include_session: bool = True) -> str:
        """
        Generate consistent filename with timestamp
        
        Args:
            base_name: Base name for the file
            extension: File extension (without dot)
            include_session: Include session ID in filename
            
        Returns:
            Generated filename
        """
        if include_session:
            return f"{base_name}_{self.session_id}.{extension}"
        else:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            return f"{base_name}_{timestamp}.{extension}"
    
    def save_extraction_results(self, results: List[ExtractionResult], 
                              version: str = "v1",
                              custom_name: Optional[str] = None) -> Path:
        """
        Save extraction results to organized directory
        
        Args:
            results: List of extraction results
            version: Version identifier (v1, v2, v3)
            custom_name: Custom filename (optional)
            
        Returns:
            Path to saved file
        """
        if custom_name:
            filename = f"{custom_name}.json"
        else:
            filename = self.generate_filename(f"{version}_extraction_results", "json")
        
        version_dir = self.extractions_dir / version
        version_dir.mkdir(parents=True, exist_ok=True)
        
        filepath = version_dir / filename
        
        # Convert to JSON-serializable format
        json_data = []
        for result in results:
            json_data.append({
                "document_id": result.document_id,
                "extraction_timestamp": result.extraction_timestamp.isoformat(),
                "total_triples": result.total_triples,
                "triples": [
                    {
                        "entity_subject": triple.entity_subject,
                        "relationship_type": triple.relationship_type,
                        "entity_object": triple.entity_object,
                        "entity_subject_type": triple.entity_subject_type,
                        "entity_object_type": triple.entity_object_type,
                        "confidence_score": triple.confidence_score,
                        "source_text_snippet": triple.source_text_snippet,
                        "extraction_method": triple.extraction_method,
                        "chunk_id": triple.chunk_id,
                        "relationship_context": triple.relationship_context,
                        "extraction_timestamp": triple.extraction_timestamp.isoformat(),
                        "entity_subject_id": triple.entity_subject_id,
                        "entity_object_id": triple.entity_object_id
                    }
                    for triple in result.triples
                ]
            })
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        return filepath
    
    def save_quality_report(self, quality_report: Dict[str, Any],
                          version: str = "v1",
                          custom_name: Optional[str] = None) -> Path:
        """
        Save quality report to organized directory
        
        Args:
            quality_report: Quality metrics dictionary
            version: Version identifier
            custom_name: Custom filename (optional)
            
        Returns:
            Path to saved file
        """
        if custom_name:
            filename = f"{custom_name}.json"
        else:
            filename = self.generate_filename(f"{version}_quality_report", "json")
        
        # Save to both version directory and session directory
        version_dir = self.quality_dir / version
        version_dir.mkdir(parents=True, exist_ok=True)
        
        version_filepath = version_dir / filename
        session_filepath = self.session_dir / filename
        
        # Add metadata to report
        enhanced_report = {
            **quality_report,
            "metadata": {
                "session_id": self.session_id,
                "version": version,
                "generated_at": datetime.now().isoformat(),
                "project_root": str(self.project_root)
            }
        }
        
        # Save to both locations
        for filepath in [version_filepath, session_filepath]:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(enhanced_report, f, indent=2, ensure_ascii=False)
        
        return version_filepath
    
    def export_csv(self, results: List[ExtractionResult],
                   custom_name: Optional[str] = None) -> Path:
        """
        Export extraction results to CSV in organized directory
        
        Args:
            results: List of extraction results
            custom_name: Custom filename (optional)
            
        Returns:
            Path to exported CSV file
        """
        if custom_name:
            filename = f"{custom_name}.csv"
        else:
            filename = self.generate_filename("extraction_results", "csv")
        
        filepath = self.csv_exports / filename
        
        # Import here to avoid circular imports
        from validation.quality_metrics import QualityMetrics
        quality_calc = QualityMetrics()
        
        # Use the existing export method but with our organized path
        quality_calc.export_to_csv(results, str(filepath))
        
        return filepath
    
    def export_json(self, results: List[ExtractionResult],
                    custom_name: Optional[str] = None) -> Path:
        """
        Export extraction results to JSON in organized directory
        
        Args:
            results: List of extraction results
            custom_name: Custom filename (optional)
            
        Returns:
            Path to exported JSON file
        """
        if custom_name:
            filename = f"{custom_name}.json"
        else:
            filename = self.generate_filename("extraction_results", "json")
        
        filepath = self.json_exports / filename
        
        # Import here to avoid circular imports
        from validation.quality_metrics import QualityMetrics
        quality_calc = QualityMetrics()
        
        # Use the existing export method but with our organized path
        quality_calc.export_to_json(results, str(filepath))
        
        return filepath
    
    def create_session_summary(self, extraction_results: List[ExtractionResult],
                             quality_report: Dict[str, Any],
                             exported_files: Dict[str, Path]) -> Path:
        """
        Create a comprehensive session summary
        
        Args:
            extraction_results: List of extraction results
            quality_report: Quality metrics report
            exported_files: Dictionary of exported file paths
            
        Returns:
            Path to session summary file
        """
        # Calculate summary statistics
        total_docs = len(extraction_results)
        total_triples = sum(len(result.triples) for result in extraction_results)
        total_entities = len(set(
            triple.entity_subject for result in extraction_results for triple in result.triples
        ).union(set(
            triple.entity_object for result in extraction_results for triple in result.triples
        )))
        
        session_summary = {
            "session_info": {
                "session_id": self.session_id,
                "start_time": self.session_id,  # Using session_id as start time
                "end_time": datetime.now().strftime('%Y%m%d_%H%M%S'),
                "project_root": str(self.project_root)
            },
            "processing_summary": {
                "documents_processed": total_docs,
                "total_triples_extracted": total_triples,
                "unique_entities_identified": total_entities,
                "avg_triples_per_document": total_triples / total_docs if total_docs > 0 else 0
            },
            "files_generated": {
                name: str(path) for name, path in exported_files.items()
            },
            "quality_metrics": quality_report.get("summary", {}),
            "next_steps": [
                "Review exported CSV/JSON files",
                "Analyze quality metrics for insights", 
                "Experiment with custom text inputs",
                "Consider scaling to Version 2 for larger documents"
            ]
        }
        
        summary_path = self.session_dir / "session_summary.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(session_summary, f, indent=2, ensure_ascii=False)
        
        return summary_path
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information"""
        return {
            "session_id": self.session_id,
            "session_dir": str(self.session_dir),
            "outputs_dir": str(self.outputs_dir),
            "project_root": str(self.project_root)
        }
    
    def list_recent_sessions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        List recent processing sessions
        
        Args:
            limit: Maximum number of sessions to return
            
        Returns:
            List of session information dictionaries
        """
        sessions_dir = self.quality_dir / "sessions"
        if not sessions_dir.exists():
            return []
        
        sessions = []
        for session_dir in sorted(sessions_dir.iterdir(), reverse=True)[:limit]:
            if session_dir.is_dir() and session_dir.name != ".gitkeep":
                summary_file = session_dir / "session_summary.json"
                if summary_file.exists():
                    try:
                        with open(summary_file, 'r', encoding='utf-8') as f:
                            summary = json.load(f)
                        sessions.append({
                            "session_id": session_dir.name,
                            "session_dir": str(session_dir),
                            "summary": summary
                        })
                    except Exception:
                        # If summary can't be read, just include basic info
                        sessions.append({
                            "session_id": session_dir.name,
                            "session_dir": str(session_dir),
                            "summary": None
                        })
        
        return sessions
    
    def cleanup_old_sessions(self, keep_recent: int = 20) -> int:
        """
        Clean up old session directories
        
        Args:
            keep_recent: Number of recent sessions to keep
            
        Returns:
            Number of sessions cleaned up
        """
        sessions_dir = self.quality_dir / "sessions"
        if not sessions_dir.exists():
            return 0
        
        # Get all session directories sorted by name (which includes timestamp)
        all_sessions = [d for d in sessions_dir.iterdir() 
                       if d.is_dir() and d.name != ".gitkeep"]
        all_sessions.sort(reverse=True)  # Most recent first
        
        # Remove old sessions
        cleaned_up = 0
        for session_dir in all_sessions[keep_recent:]:
            try:
                shutil.rmtree(session_dir)
                cleaned_up += 1
            except Exception:
                pass  # Skip if can't delete
        
        return cleaned_up
    
    def get_directory_sizes(self) -> Dict[str, int]:
        """Get sizes of output directories in bytes"""
        def get_dir_size(path: Path) -> int:
            total = 0
            if path.exists():
                for file_path in path.rglob('*'):
                    if file_path.is_file():
                        total += file_path.stat().st_size
            return total
        
        return {
            "extractions": get_dir_size(self.extractions_dir),
            "quality_reports": get_dir_size(self.quality_dir), 
            "exports": get_dir_size(self.exports_dir),
            "total": get_dir_size(self.outputs_dir)
        }