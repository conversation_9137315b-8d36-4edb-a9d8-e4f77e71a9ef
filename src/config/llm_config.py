"""
LLM Configuration for Graph Ingestion Pipeline
Extracted from LLMExtractor.__init__() for proper config management
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load .env file when this module is imported
load_dotenv()


# LLM configurations - all values read from environment variables
LLM_CONFIGS: Dict[str, Dict[str, Any]] = {
    "ollama": {
        "provider": "ollama",
        "model": os.getenv("OLLAMA_MODEL", "llama3.2:latest"),
        "temperature": float(os.getenv("OLLAMA_TEMPERATURE", "0.1")),
        "max_tokens": int(os.getenv("OLLAMA_MAX_TOKENS", "4000")),
        "api_key_env": None,  # Ollama typically doesn't need API keys
        "base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
        "client_class": "ollama.Client"
    },
    "anthropic": {
        "provider": "anthropic",
        "model": os.getenv("ANTHROPIC_MODEL", "claude-3-5-sonnet-20241022"),
        "temperature": float(os.getenv("ANTHROPIC_TEMPERATURE", "0.1")),
        "max_tokens": int(os.getenv("ANTHROPIC_MAX_TOKENS", "4000")),
        "api_key_env": "ANTHROPIC_API_KEY",
        "client_class": "anthropic.Anthropic"
    },
    "openai": {
        "provider": "openai",
        "model": os.getenv("OPENAI_MODEL", "gpt-4o-mini"),
        "temperature": float(os.getenv("OPENAI_TEMPERATURE", "0.1")),
        "max_tokens": int(os.getenv("OPENAI_MAX_TOKENS", "4000")),
        "api_key_env": "OPENAI_API_KEY",
        "client_class": "openai.OpenAI"
    }
}


def get_llm_config(provider: Optional[str] = None, model: Optional[str] = None) -> Dict[str, Any]:
    """
    Get LLM configuration from environment variables

    Args:
        provider: Specific provider ("openai", "anthropic", "ollama"). If None, auto-detect from env
        model: Override default model for the provider

    Returns:
        Dict with LLM configuration

    Raises:
        ValueError: If provider is not supported
        EnvironmentError: If required API key environment variable is missing
    """
    # Auto-detect provider if not specified
    if provider is None:
        provider = _detect_provider_from_env()

    # Validate provider exists in config
    if provider not in LLM_CONFIGS:
        available = list(LLM_CONFIGS.keys())
        raise ValueError(f"Unsupported provider: {provider}. Available: {available}")

    # Get base config (already reads from env vars)
    config = LLM_CONFIGS[provider].copy()

    # Override model if specified
    if model:
        config["model"] = model

    # Validate API key is available (skip for providers that don't need keys)
    api_key_env = config["api_key_env"]
    if api_key_env:  # Only check if API key is required
        api_key = os.getenv(api_key_env)
        if not api_key:
            raise EnvironmentError(
                f"Required environment variable {api_key_env} is not set. "
                f"Please set your {provider.upper()} API key."
            )

    # For Ollama, check if service is available
    if provider == "ollama":
        base_url = config.get("base_url", "http://localhost:11434")
        if not _is_ollama_available(base_url):
            raise EnvironmentError(
                f"Ollama service not available at {base_url}. "
                f"Please start Ollama service or set OLLAMA_BASE_URL."
            )

    return config


def _detect_provider_from_env() -> str:
    """
    Auto-detect LLM provider based on available API keys or services

    Returns:
        Provider name ("openai", "anthropic", or "ollama")

    Raises:
        EnvironmentError: If no providers are available
    """
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    ollama_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

    if anthropic_key:
        return "anthropic"
    elif openai_key:
        return "openai"
    elif _is_ollama_available(ollama_url):
        return "ollama"
    else:
        raise EnvironmentError(
            "No LLM providers found. Please set API keys (OPENAI_API_KEY or ANTHROPIC_API_KEY) "
            "or start Ollama service. See .env.example for details."
        )


def _is_ollama_available(url: str) -> bool:
    """
    Check if Ollama service is running

    Args:
        url: Ollama service URL

    Returns:
        True if Ollama service is available
    """
    try:
        import requests
        response = requests.get(f"{url}/api/tags", timeout=2)
        return response.status_code == 200
    except Exception:
        return False

# Currently not used
def _is_placeholder_key(api_key: str) -> bool:
    """
    Check if API key is a placeholder value from .env.example

    Args:
        api_key: API key string to validate

    Returns:
        True if the key appears to be a placeholder
    """
    placeholder_patterns = [
        "sk-your-openai-key-here",
        "sk-ant-your-anthropic-key-here",
        "your-openai-key-here",
        "your-anthropic-key-here",
        "sk-your-key-here",
        "your-api-key-here"
    ]

    # Check exact matches with common placeholders
    if api_key.lower() in [p.lower() for p in placeholder_patterns]:
        return True

    # Check for obvious placeholder patterns
    if "your-" in api_key.lower() and "-key" in api_key.lower():
        return True

    # Check for very short keys (real keys are much longer)
    if len(api_key) < 20:
        return True

    return False


def validate_llm_config(config: Dict[str, Any]) -> bool:
    """
    Validate LLM configuration has required fields
    
    Args:
        config: LLM configuration dict
        
    Returns:
        True if valid
        
    Raises:
        ValueError: If configuration is invalid
    """
    required_fields = ["provider", "model", "temperature", "max_tokens", "api_key_env"]
    
    for field in required_fields:
        if field not in config:
            raise ValueError(f"LLM config missing required field: {field}")
    
    # Validate provider is supported
    if config["provider"] not in LLM_CONFIGS:
        available = list(LLM_CONFIGS.keys())
        raise ValueError(f"Invalid provider in config: {config['provider']}. Available: {available}")
    
    return True
