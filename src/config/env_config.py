"""
Environment Configuration Documentation
Lists all required and optional environment variables
"""

from typing import Dict, List, Any


# Required environment variables for core functionality
REQUIRED_ENV_VARS: Dict[str, str] = {
    # LLM API Keys (at least one required)
    "OPENAI_API_KEY": "OpenAI API key for GPT models (sk-...)",
    "ANTHROPIC_API_KEY": "Anthropic API key for Claude models (sk-ant-...)",
}

# Optional environment variables with defaults
OPTIONAL_ENV_VARS: Dict[str, Dict[str, Any]] = {
    # Memgraph connection (optional, defaults to localhost)
    "MEMGRAPH_HOST": {
        "description": "Memgraph database host",
        "default": "localhost"
    },
    "MEMGRAPH_PORT": {
        "description": "Memgraph database port", 
        "default": 7687
    },
    "MEMGRAPH_USERNAME": {
        "description": "Memgraph username (if auth enabled)",
        "default": ""
    },
    "MEMGRAPH_PASSWORD": {
        "description": "Memgraph password (if auth enabled)",
        "default": ""
    },
    
    # Model preferences (optional, uses provider defaults)
    "DEFAULT_OPENAI_MODEL": {
        "description": "Override default OpenAI model",
        "default": "gpt-4o-mini"
    },
    "DEFAULT_ANTHROPIC_MODEL": {
        "description": "Override default Anthropic model",
        "default": "claude-3-5-sonnet-20241022"
    },
    "DEFAULT_OLLAMA_MODEL": {
        "description": "Override default Ollama model",
        "default": "llama3.1:8b"
    },

    # Ollama settings (optional)
    "OLLAMA_BASE_URL": {
        "description": "Ollama service URL",
        "default": "http://localhost:11434"
    },
    
    # Processing settings (optional)
    "DEFAULT_CONFIDENCE_THRESHOLD": {
        "description": "Minimum confidence for extracted triples",
        "default": 0.7
    },
    "MAX_RETRIES": {
        "description": "Maximum retries for failed operations",
        "default": 3
    },
    "EXTRACTION_TIMEOUT": {
        "description": "Timeout for LLM extraction (seconds)",
        "default": 120
    }
}


def get_env_requirements() -> Dict[str, List[str]]:
    """
    Get environment variable requirements summary
    
    Returns:
        Dict with required and optional env var lists
    """
    return {
        "required": list(REQUIRED_ENV_VARS.keys()),
        "optional": list(OPTIONAL_ENV_VARS.keys())
    }


def validate_required_env() -> Dict[str, bool]:
    """
    Check which required environment variables are set
    
    Returns:
        Dict mapping env var names to availability status
    """
    import os
    
    status = {}
    for env_var in REQUIRED_ENV_VARS:
        status[env_var] = bool(os.getenv(env_var))
    
    return status


def get_missing_env_vars() -> List[str]:
    """
    Get list of missing required environment variables
    
    Returns:
        List of missing env var names
    """
    status = validate_required_env()
    return [var for var, available in status.items() if not available]
