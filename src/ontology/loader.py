"""
Ontology loader for parsing Cypher files into structured Python dictionaries
"""

import re
from typing import Dict, Any, List
from pathlib import Path


def load_ontology_from_cypher(cypher_file_path: str) -> Dict[str, Any]:
    """
    Load ontology from a Cypher file path
    
    Args:
        cypher_file_path: Path to the .cypher file containing ontology definitions
        
    Returns:
        Dict with parsed ontology structure:
        {
            "entities": {
                "Person": {
                    "description": "Individuals (employees, customers, contacts, leaders)",
                    "properties": ["name", "email", "phone", "title"]
                }
            },
            "relationships": {
                "works_at": {
                    "description": "Person employed by Organization",
                    "source_entities": ["Person"],
                    "target_entities": ["Organization"],
                    "properties": ["start_date", "role", "department"]
                }
            }
        }
        
    Raises:
        FileNotFoundError: If cypher file doesn't exist
        ValueError: If cypher file has invalid format
    """
    if not Path(cypher_file_path).exists():
        raise FileNotFoundError(f"Ontology file not found: {cypher_file_path}")
    
    with open(cypher_file_path, 'r', encoding='utf-8') as f:
        cypher_content = f.read()
    
    return load_ontology_from_string(cypher_content)


def load_ontology_from_string(cypher_content: str) -> Dict[str, Any]:
    """
    Load ontology from Cypher string content
    
    Args:
        cypher_content: String containing Cypher CREATE statements
        
    Returns:
        Dict with parsed ontology structure (same as load_ontology_from_cypher)
        
    Raises:
        ValueError: If cypher content has invalid format
    """
    ontology = {
        "entities": {},
        "relationships": {}
    }
    
    # Parse EntityType definitions
    entity_pattern = r'CREATE \(:EntityType \{([^}]+)\}\);'
    entity_matches = re.findall(entity_pattern, cypher_content, re.DOTALL)
    
    for match in entity_matches:
        entity_data = _parse_cypher_properties(match)
        if "name" in entity_data:
            name = entity_data["name"]
            ontology["entities"][name] = {
                "description": entity_data.get("description", ""),
                "properties": entity_data.get("properties", [])
            }
    
    # Parse RelationshipType definitions
    relationship_pattern = r'CREATE \(:RelationshipType \{([^}]+)\}\);'
    relationship_matches = re.findall(relationship_pattern, cypher_content, re.DOTALL)
    
    for match in relationship_matches:
        rel_data = _parse_cypher_properties(match)
        if "name" in rel_data:
            name = rel_data["name"]
            ontology["relationships"][name] = {
                "description": rel_data.get("description", ""),
                "source_entities": rel_data.get("source_entities", []),
                "target_entities": rel_data.get("target_entities", []),
                "properties": rel_data.get("properties", [])
            }
    
    # Validate parsed ontology
    if not ontology["entities"] or not ontology["relationships"]:
        raise ValueError("Invalid Cypher format: No entities or relationships found")
    
    return ontology


def _parse_cypher_properties(properties_string: str) -> Dict[str, Any]:
    """
    Parse Cypher property string into Python dict
    
    Args:
        properties_string: String like 'name: "Person", description: "Individual", properties: ["name", "email"]'
        
    Returns:
        Dict with parsed properties
    """
    result = {}
    
    # Parse simple string properties (name, description)
    string_pattern = r'(\w+):\s*"([^"]*)"'
    string_matches = re.findall(string_pattern, properties_string)
    for key, value in string_matches:
        result[key] = value
    
    # Parse array properties
    array_pattern = r'(\w+):\s*\[([^\]]*)\]'
    array_matches = re.findall(array_pattern, properties_string)
    for key, value in array_matches:
        # Parse array elements (remove quotes and whitespace)
        elements = [elem.strip().strip('"') for elem in value.split(',') if elem.strip()]
        result[key] = elements
    
    return result
