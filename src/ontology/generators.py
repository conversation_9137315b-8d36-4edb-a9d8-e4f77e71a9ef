"""
Generators to convert ontology dict into different formats:
1. Human-readable prompt text for LLM instruction
2. Pydantic enums for validation
3. JSON schema for function calling
"""

from typing import Dict, Any, Tuple
from enum import Enum


def generate_prompt_ontology(ontology: Dict[str, Any]) -> str:
    """
    Generate human-readable ontology text for LLM prompts
    
    Args:
        ontology: Parsed ontology dict from loader
        
    Returns:
        Formatted string for LLM instruction
    """
    prompt_parts = []
    
    # Entity Types section
    prompt_parts.append("ALLOWED Entity Types (use EXACTLY these values):")
    for entity_name, entity_data in ontology["entities"].items():
        description = entity_data.get("description", "")
        prompt_parts.append(f"- {entity_name}: {description}")
    
    prompt_parts.append("")  # Empty line
    
    # Relationship Types section
    prompt_parts.append("ALLOWED Relationship Types (use EXACTLY these values):")
    for rel_name, rel_data in ontology["relationships"].items():
        description = rel_data.get("description", "")
        source_entities = rel_data.get("source_entities", [])
        target_entities = rel_data.get("target_entities", [])

        # Build detailed relationship description with entity constraints
        if source_entities and target_entities:
            source_str = " or ".join(source_entities)
            target_str = " or ".join(target_entities)
            detailed_desc = f"{description} (Source: {source_str} → Target: {target_str})"
        else:
            detailed_desc = description

        prompt_parts.append(f"- {rel_name}: {detailed_desc}")
    
    return "\n".join(prompt_parts)


def generate_pydantic_enums(ontology: Dict[str, Any]) -> Tuple[type, type]:
    """
    Generate Pydantic enum classes from ontology
    
    Args:
        ontology: Parsed ontology dict from loader
        
    Returns:
        Tuple of (EntityType enum class, RelationshipType enum class)
    """
    # Create EntityType enum
    entity_members = {}
    for entity_name in ontology["entities"].keys():
        # Convert "Person" -> "PERSON" for enum member name
        enum_name = entity_name.upper().replace(" ", "_")
        entity_members[enum_name] = entity_name
    
    EntityType = Enum("EntityType", entity_members, type=str)
    
    # Create RelationshipType enum  
    relationship_members = {}
    for rel_name in ontology["relationships"].keys():
        # Convert "works_at" -> "WORKS_AT" for enum member name
        enum_name = rel_name.upper().replace(" ", "_")
        relationship_members[enum_name] = rel_name
    
    RelationshipType = Enum("RelationshipType", relationship_members, type=str)
    
    return EntityType, RelationshipType


def generate_function_schema(ontology: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate JSON schema for LLM function calling
    
    Args:
        ontology: Parsed ontology dict from loader
        
    Returns:
        Function calling schema dict
    """
    entity_values = list(ontology["entities"].keys())
    relationship_values = list(ontology["relationships"].keys())
    
    schema = {
        "type": "function",
        "function": {
            "name": "extract_entities_relationships",
            "description": "Extract entities and relationships from text according to the predefined ontology",
            "parameters": {
                "type": "object",
                "properties": {
                    "triples": {
                        "type": "array",
                        "description": "List of extracted entity-relationship triples",
                        "items": {
                            "type": "object",
                            "properties": {
                                "entity_subject": {
                                    "type": "string",
                                    "description": "The subject entity name"
                                },
                                "relationship_type": {
                                    "type": "string",
                                    "enum": relationship_values,
                                    "description": "The relationship type (must be from predefined ontology)"
                                },
                                "entity_object": {
                                    "type": "string", 
                                    "description": "The object entity name"
                                },
                                "entity_subject_type": {
                                    "type": "string",
                                    "enum": entity_values,
                                    "description": "The subject entity type (must be from predefined ontology)"
                                },
                                "entity_object_type": {
                                    "type": "string",
                                    "enum": entity_values,
                                    "description": "The object entity type (must be from predefined ontology)"
                                },
                                "source_text_snippet": {
                                    "type": "string",
                                    "description": "The text snippet that supports this relationship"
                                },
                                "relationship_context": {
                                    "type": "string",
                                    "description": "Additional context about the relationship"
                                }
                            },
                            "required": [
                                "entity_subject",
                                "relationship_type", 
                                "entity_object",
                                "entity_subject_type",
                                "entity_object_type",
                                "source_text_snippet",
                                "relationship_context"
                            ]
                        }
                    }
                },
                "required": ["triples"]
            }
        }
    }
    
    return schema


def generate_validation_models(ontology: Dict[str, Any]) -> str:
    """
    Generate Pydantic model code as string (for dynamic model creation)
    
    Args:
        ontology: Parsed ontology dict from loader
        
    Returns:
        Python code string defining Pydantic models
    """
    entity_values = list(ontology["entities"].keys())
    relationship_values = list(ontology["relationships"].keys())
    
    # Generate enum definitions
    entity_enum_members = []
    for entity_name in entity_values:
        enum_name = entity_name.upper().replace(" ", "_")
        entity_enum_members.append(f'    {enum_name} = "{entity_name}"')
    
    relationship_enum_members = []
    for rel_name in relationship_values:
        enum_name = rel_name.upper().replace(" ", "_")
        relationship_enum_members.append(f'    {enum_name} = "{rel_name}"')
    
    code = f'''from enum import Enum
from pydantic import BaseModel
from typing import List

class EntityType(str, Enum):
    """Generated entity types from ontology"""
{chr(10).join(entity_enum_members)}

class RelationshipType(str, Enum):
    """Generated relationship types from ontology"""
{chr(10).join(relationship_enum_members)}

class TripleResponse(BaseModel):
    """Pydantic model for validating individual triple from LLM response"""
    entity_subject: str
    relationship_type: RelationshipType
    entity_object: str
    entity_subject_type: EntityType
    entity_object_type: EntityType
    source_text_snippet: str
    relationship_context: str

class ExtractionResponse(BaseModel):
    """Pydantic model for validating complete LLM extraction response"""
    triples: List[TripleResponse]
'''
    
    return code
