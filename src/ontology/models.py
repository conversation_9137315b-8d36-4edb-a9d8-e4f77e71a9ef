"""
Ontology models for Version 1 - LLM + Function Calling
Defines entity types, relationship types, and tabular schema
"""

from typing import List, Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class EntityType(str, Enum):
    """Supported entity types from the ontology"""
    PERSON = "Person"
    ORGANIZATION = "Organization"
    LOCATION = "Location"
    PROJECT = "Project"
    EVENT = "Event"
    PRODUCT = "Product"
    ROLE = "Role"


class RelationshipType(str, Enum):
    """Supported relationship types from the ontology"""
    WORKS_FOR = "works_for"
    LOCATED_AT = "located_at"
    MANAGES = "manages"
    PARTICIPATES_IN = "participates_in"
    REPORTS_TO = "reports_to"
    COLLABORATES_WITH = "collaborates_with"
    OCCURRED_AT = "occurred_at"
    DEVELOPS = "develops"
    HOLDS_ROLE = "holds_role"


class EntityRelationshipTriple(BaseModel):
    """
    Core entity-relationship triple with full 14-column schema
    This represents one row in the tabular output
    """
    # Core triple attributes (for graph construction)
    entity_subject: str = Field(description="Source entity name")
    relationship_type: RelationshipType = Field(description="Relationship/edge type from ontology")
    entity_object: str = Field(description="Target entity name")
    entity_subject_type: EntityType = Field(description="Source entity category")
    entity_object_type: EntityType = Field(description="Target entity category")
    
    # Metadata attributes (for GraphRAG & quality)
    confidence_score: float = Field(default=0.85, description="Extraction confidence (0.0-1.0)")
    source_text_snippet: str = Field(description="Original text supporting this relationship")
    extraction_method: str = Field(default="llm_function_calling", description="Extraction approach used")
    document_id: str = Field(description="Source document identifier")
    chunk_id: str = Field(default="full_text", description="Specific text chunk identifier")
    relationship_context: str = Field(description="Semantic context of relationship")
    extraction_timestamp: datetime = Field(default_factory=datetime.now, description="When extraction occurred")
    entity_subject_id: str = Field(description="Unique ID for subject entity (deduplication)")
    entity_object_id: str = Field(description="Unique ID for object entity (deduplication)")

    model_config = {"use_enum_values": True}


class ExtractionResult(BaseModel):
    """
    Result of LLM extraction containing multiple triples
    """
    triples: List[EntityRelationshipTriple] = Field(description="List of extracted entity-relationship triples")
    document_id: str = Field(description="Source document identifier")
    extraction_timestamp: datetime = Field(default_factory=datetime.now)
    total_triples: int = Field(description="Total number of triples extracted")
    
    def __post_init__(self):
        self.total_triples = len(self.triples)


class Entity(BaseModel):
    """
    Individual entity for graph construction
    """
    id: str = Field(description="Unique entity identifier")
    name: str = Field(description="Entity name")
    type: EntityType = Field(description="Entity category")
    
    model_config = {"use_enum_values": True}


class Relationship(BaseModel):
    """
    Individual relationship for graph construction
    """
    subject_id: str = Field(description="Subject entity ID")
    predicate: RelationshipType = Field(description="Relationship type")
    object_id: str = Field(description="Object entity ID")
    confidence_score: float = Field(description="Relationship confidence")
    source_text_snippet: str = Field(description="Supporting text")
    document_id: str = Field(description="Source document")
    
    model_config = {"use_enum_values": True}


class GraphData(BaseModel):
    """
    Complete graph data for Memgraph insertion
    """
    entities: List[Entity] = Field(description="All unique entities")
    relationships: List[Relationship] = Field(description="All relationships")
    metadata: dict = Field(description="Processing metadata")


# LLM Function Calling Schemas
EXTRACTION_TOOL_SCHEMA = {
    "type": "function",
    "function": {
        "name": "extract_entities_relationships",
        "description": "Extract entities and relationships from text according to predefined ontology",
        "parameters": {
            "type": "object",
            "properties": {
                "triples": {
                    "type": "array",
                    "description": "List of entity-relationship triples",
                    "items": {
                        "type": "object",
                        "properties": {
                            "entity_subject": {"type": "string", "description": "Source entity name"},
                            "relationship_type": {
                                "type": "string",
                                "enum": [r.value for r in RelationshipType],
                                "description": "Relationship type from ontology"
                            },
                            "entity_object": {"type": "string", "description": "Target entity name"},
                            "entity_subject_type": {
                                "type": "string",
                                "enum": [e.value for e in EntityType],
                                "description": "Source entity category"
                            },
                            "entity_object_type": {
                                "type": "string",
                                "enum": [e.value for e in EntityType],
                                "description": "Target entity category"
                            },
                            "source_text_snippet": {"type": "string", "description": "Supporting text from original"},
                            "relationship_context": {"type": "string", "description": "Semantic context of relationship"}
                        },
                        "required": ["entity_subject", "relationship_type", "entity_object",
                                   "entity_subject_type", "entity_object_type",
                                   "source_text_snippet", "relationship_context"]
                    }
                }
            },
            "required": ["triples"]
        }
    }
}


# Pydantic models for LLM response validation
class TripleResponse(BaseModel):
    """Pydantic model for validating individual triple from LLM response"""
    entity_subject: str
    relationship_type: RelationshipType
    entity_object: str
    entity_subject_type: EntityType
    entity_object_type: EntityType
    source_text_snippet: str
    relationship_context: str


class ExtractionResponse(BaseModel):
    """Pydantic model for validating complete LLM extraction response"""
    triples: List[TripleResponse]