"""
Memgraph client for graph construction and querying
Version 1 implementation with basic graph operations
"""

import os
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

import pymgclient
from pymgclient import DatabaseError

from ontology.models import (
    EntityRelationshipTriple, 
    ExtractionResult, 
    Entity, 
    Relationship, 
    GraphData
)


class MemgraphClient:
    """
    Client for interacting with Memgraph database
    """
    
    def __init__(self, host: str = "localhost", port: int = 7687, username: str = "", password: str = ""):
        """
        Initialize Memgraph client
        
        Args:
            host: Memgraph host
            port: Memgraph port
            username: Userna<PERSON> (optional)
            password: Password (optional)
        """
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.connection = None
        
    def connect(self):
        """Establish connection to Memgraph"""
        try:
            self.connection = pymgclient.connect(
                host=self.host,
                port=self.port,
                username=self.username,
                password=self.password
            )
            print(f"Connected to Mem<PERSON> at {self.host}:{self.port}")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to Memgraph: {str(e)}")
    
    def disconnect(self):
        """Close connection to Memgraph"""
        if self.connection:
            self.connection.close()
            print("Disconnected from Memgraph")
    
    def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Execute a Cypher query
        
        Args:
            query: Cypher query string
            parameters: Query parameters
            
        Returns:
            List of result records
        """
        if not self.connection:
            raise RuntimeError("Not connected to Memgraph")
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, parameters or {})
            
            # Fetch results
            results = []
            while True:
                row = cursor.fetchone()
                if row is None:
                    break
                
                # Convert row to dictionary
                columns = [desc[0] for desc in cursor.description]
                row_dict = dict(zip(columns, row))
                results.append(row_dict)
            
            return results
            
        except DatabaseError as e:
            raise RuntimeError(f"Database error: {str(e)}")
    
    def setup_schema(self):
        """Set up graph schema with indexes"""
        schema_queries = [
            # Create indexes for efficient lookups
            "CREATE INDEX ON :Entity(id);",
            "CREATE INDEX ON :Entity(name);",
            "CREATE INDEX ON :Entity(type);",
            "CREATE INDEX ON :Person(name);",
            "CREATE INDEX ON :Organization(name);",
            "CREATE INDEX ON :Location(name);",
            "CREATE INDEX ON :Project(name);",
            "CREATE INDEX ON :Event(name);",
            "CREATE INDEX ON :Product(name);",
            "CREATE INDEX ON :Role(name);",
        ]
        
        for query in schema_queries:
            try:
                self.execute_query(query)
                print(f"Executed: {query}")
            except Exception as e:
                if "already exists" in str(e).lower():
                    print(f"Index already exists: {query}")
                else:
                    print(f"Warning: Failed to execute {query}: {str(e)}")
    
    def clear_graph(self):
        """Clear all nodes and relationships"""
        query = "MATCH (n) DETACH DELETE n;"
        self.execute_query(query)
        print("Graph cleared")
    
    def create_entity(self, entity: Entity) -> bool:
        """
        Create an entity node in the graph
        
        Args:
            entity: Entity object
            
        Returns:
            Success status
        """
        query = f"""
        MERGE (e:Entity {{id: $id}})
        SET e.name = $name, e.type = $type
        SET e:{entity.type.value}
        RETURN e
        """
        
        parameters = {
            "id": entity.id,
            "name": entity.name,
            "type": entity.type.value
        }
        
        try:
            results = self.execute_query(query, parameters)
            return len(results) > 0
        except Exception as e:
            print(f"Error creating entity {entity.name}: {str(e)}")
            return False
    
    def create_relationship(self, relationship: Relationship) -> bool:
        """
        Create a relationship between entities
        
        Args:
            relationship: Relationship object
            
        Returns:
            Success status
        """
        query = f"""
        MATCH (subject:Entity {{id: $subject_id}})
        MATCH (object:Entity {{id: $object_id}})
        MERGE (subject)-[r:{relationship.predicate.value.upper()}]->(object)
        SET r.confidence_score = $confidence_score,
            r.source_text_snippet = $source_text_snippet,
            r.document_id = $document_id,
            r.created_at = $created_at
        RETURN r
        """
        
        parameters = {
            "subject_id": relationship.subject_id,
            "object_id": relationship.object_id,
            "confidence_score": relationship.confidence_score,
            "source_text_snippet": relationship.source_text_snippet,
            "document_id": relationship.document_id,
            "created_at": datetime.now().isoformat()
        }
        
        try:
            results = self.execute_query(query, parameters)
            return len(results) > 0
        except Exception as e:
            print(f"Error creating relationship {relationship.predicate}: {str(e)}")
            return False
    
    def insert_extraction_result(self, result: ExtractionResult) -> bool:
        """
        Insert complete extraction result into graph
        
        Args:
            result: ExtractionResult object
            
        Returns:
            Success status
        """
        try:
            # Convert to graph data
            graph_data = self._extraction_result_to_graph_data(result)
            
            # Insert entities
            entities_created = 0
            for entity in graph_data.entities:
                if self.create_entity(entity):
                    entities_created += 1
            
            # Insert relationships
            relationships_created = 0
            for relationship in graph_data.relationships:
                if self.create_relationship(relationship):
                    relationships_created += 1
            
            print(f"Inserted {entities_created} entities and {relationships_created} relationships")
            return True
            
        except Exception as e:
            print(f"Error inserting extraction result: {str(e)}")
            return False
    
    def _extraction_result_to_graph_data(self, result: ExtractionResult) -> GraphData:
        """Convert extraction result to graph data"""
        entities = []
        relationships = []
        entity_ids = set()
        
        for triple in result.triples:
            # Create subject entity
            if triple.entity_subject_id not in entity_ids:
                subject_entity = Entity(
                    id=triple.entity_subject_id,
                    name=triple.entity_subject,
                    type=triple.entity_subject_type
                )
                entities.append(subject_entity)
                entity_ids.add(triple.entity_subject_id)
            
            # Create object entity
            if triple.entity_object_id not in entity_ids:
                object_entity = Entity(
                    id=triple.entity_object_id,
                    name=triple.entity_object,
                    type=triple.entity_object_type
                )
                entities.append(object_entity)
                entity_ids.add(triple.entity_object_id)
            
            # Create relationship
            relationship = Relationship(
                subject_id=triple.entity_subject_id,
                predicate=triple.relationship_type,
                object_id=triple.entity_object_id,
                confidence_score=triple.confidence_score,
                source_text_snippet=triple.source_text_snippet,
                document_id=triple.document_id
            )
            relationships.append(relationship)
        
        return GraphData(
            entities=entities,
            relationships=relationships,
            metadata={
                "document_id": result.document_id,
                "extraction_timestamp": result.extraction_timestamp.isoformat(),
                "total_triples": result.total_triples
            }
        )
    
    def get_entity_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Find entity by name"""
        query = "MATCH (e:Entity {name: $name}) RETURN e"
        results = self.execute_query(query, {"name": name})
        return results[0] if results else None
    
    def get_relationships_for_entity(self, entity_id: str) -> List[Dict[str, Any]]:
        """Get all relationships for an entity"""
        query = """
        MATCH (e:Entity {id: $entity_id})-[r]-(connected)
        RETURN e, r, connected
        """
        return self.execute_query(query, {"entity_id": entity_id})
    
    def get_graph_stats(self) -> Dict[str, Any]:
        """Get graph statistics"""
        queries = {
            "total_entities": "MATCH (e:Entity) RETURN count(e) as count",
            "total_relationships": "MATCH ()-[r]->() RETURN count(r) as count",
            "entities_by_type": "MATCH (e:Entity) RETURN e.type as type, count(e) as count",
            "relationships_by_type": "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
        }
        
        stats = {}
        for key, query in queries.items():
            try:
                results = self.execute_query(query)
                stats[key] = results
            except Exception as e:
                stats[key] = f"Error: {str(e)}"
        
        return stats
    
    def query_graph(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute custom graph query"""
        return self.execute_query(query, parameters)
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()