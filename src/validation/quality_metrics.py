"""
Quality metrics and validation for Version 1
Basic accuracy and completeness tracking
"""

import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json

from ontology.models import ExtractionResult, EntityRelationshipTriple


class QualityMetrics:
    """
    Quality metrics calculator for extraction results
    """
    
    def __init__(self):
        self.metrics_history = []
    
    def calculate_basic_metrics(self, result: ExtractionResult) -> Dict[str, Any]:
        """
        Calculate basic quality metrics for an extraction result
        
        Args:
            result: ExtractionResult to analyze
            
        Returns:
            Dictionary of metrics
        """
        triples = result.triples
        
        if not triples:
            return {
                "total_triples": 0,
                "avg_confidence": 0.0,
                "entity_types_coverage": {},
                "relationship_types_coverage": {},
                "avg_snippet_length": 0.0,
                "extraction_timestamp": result.extraction_timestamp.isoformat(),
                "document_id": result.document_id
            }
        
        # Basic counts
        total_triples = len(triples)
        avg_confidence = sum(t.confidence_score for t in triples) / total_triples
        
        # Entity type coverage
        entity_types = {}
        for triple in triples:
            subj_type = triple.entity_subject_type
            obj_type = triple.entity_object_type
            entity_types[subj_type] = entity_types.get(subj_type, 0) + 1
            entity_types[obj_type] = entity_types.get(obj_type, 0) + 1
        
        # Relationship type coverage
        relationship_types = {}
        for triple in triples:
            rel_type = triple.relationship_type
            relationship_types[rel_type] = relationship_types.get(rel_type, 0) + 1
        
        # Text snippet analysis
        avg_snippet_length = sum(len(t.source_text_snippet) for t in triples) / total_triples
        
        metrics = {
            "total_triples": total_triples,
            "avg_confidence": avg_confidence,
            "entity_types_coverage": entity_types,
            "relationship_types_coverage": relationship_types,
            "avg_snippet_length": avg_snippet_length,
            "extraction_timestamp": result.extraction_timestamp.isoformat(),
            "document_id": result.document_id
        }
        
        self.metrics_history.append(metrics)
        return metrics
    
    def calculate_entity_metrics(self, result: ExtractionResult) -> Dict[str, Any]:
        """Calculate entity-specific metrics"""
        triples = result.triples
        
        # Unique entities
        unique_subjects = set(t.entity_subject for t in triples)
        unique_objects = set(t.entity_object for t in triples)
        unique_entities = unique_subjects.union(unique_objects)
        
        # Entity connectivity (how many relationships each entity has)
        entity_connections = {}
        for triple in triples:
            subj = triple.entity_subject
            obj = triple.entity_object
            entity_connections[subj] = entity_connections.get(subj, 0) + 1
            entity_connections[obj] = entity_connections.get(obj, 0) + 1
        
        # Most connected entities
        most_connected = sorted(entity_connections.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "unique_entities": len(unique_entities),
            "unique_subjects": len(unique_subjects),
            "unique_objects": len(unique_objects),
            "avg_connections_per_entity": sum(entity_connections.values()) / len(entity_connections) if entity_connections else 0,
            "most_connected_entities": most_connected,
            "entity_connections": entity_connections
        }
    
    def validate_extraction_result(self, result: ExtractionResult) -> Dict[str, Any]:
        """
        Validate extraction result for common issues
        
        Args:
            result: ExtractionResult to validate
            
        Returns:
            Validation report
        """
        issues = []
        warnings = []
        
        # Check for empty results
        if not result.triples:
            issues.append("No triples extracted")
        
        # Check individual triples
        for i, triple in enumerate(result.triples):
            # Check for empty fields
            if not triple.entity_subject.strip():
                issues.append(f"Triple {i}: Empty subject entity")
            if not triple.entity_object.strip():
                issues.append(f"Triple {i}: Empty object entity")
            if not triple.source_text_snippet.strip():
                issues.append(f"Triple {i}: Empty source text snippet")
            
            # Check confidence score
            if triple.confidence_score < 0.5:
                warnings.append(f"Triple {i}: Low confidence score ({triple.confidence_score:.2f})")
            
            # Check for very short snippets
            if len(triple.source_text_snippet) < 10:
                warnings.append(f"Triple {i}: Very short source snippet")
            
            # Check for duplicate entities (case-insensitive)
            if triple.entity_subject.lower() == triple.entity_object.lower():
                warnings.append(f"Triple {i}: Subject and object are the same entity")
        
        # Check for potential duplicates
        triple_signatures = set()
        for i, triple in enumerate(result.triples):
            signature = f"{triple.entity_subject}|{triple.relationship_type}|{triple.entity_object}".lower()
            if signature in triple_signatures:
                warnings.append(f"Triple {i}: Potential duplicate relationship")
            triple_signatures.add(signature)
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "total_issues": len(issues),
            "total_warnings": len(warnings)
        }
    
    def generate_quality_report(self, results: List[ExtractionResult]) -> Dict[str, Any]:
        """
        Generate comprehensive quality report for multiple results
        
        Args:
            results: List of ExtractionResult objects
            
        Returns:
            Quality report
        """
        if not results:
            return {"error": "No results to analyze"}
        
        # Calculate metrics for each result
        all_metrics = []
        all_validations = []
        
        for result in results:
            metrics = self.calculate_basic_metrics(result)
            entity_metrics = self.calculate_entity_metrics(result)
            validation = self.validate_extraction_result(result)
            
            combined_metrics = {**metrics, **entity_metrics, "validation": validation}
            all_metrics.append(combined_metrics)
            all_validations.append(validation)
        
        # Aggregate statistics
        total_triples = sum(m["total_triples"] for m in all_metrics)
        avg_confidence = sum(m["avg_confidence"] for m in all_metrics) / len(all_metrics)
        avg_entities_per_doc = sum(m["unique_entities"] for m in all_metrics) / len(all_metrics)
        
        # Validation statistics
        valid_extractions = sum(1 for v in all_validations if v["is_valid"])
        total_issues = sum(v["total_issues"] for v in all_validations)
        total_warnings = sum(v["total_warnings"] for v in all_validations)
        
        # Entity and relationship type distributions
        all_entity_types = {}
        all_relationship_types = {}
        
        for metrics in all_metrics:
            for entity_type, count in metrics["entity_types_coverage"].items():
                all_entity_types[entity_type] = all_entity_types.get(entity_type, 0) + count
            
            for rel_type, count in metrics["relationship_types_coverage"].items():
                all_relationship_types[rel_type] = all_relationship_types.get(rel_type, 0) + count
        
        return {
            "summary": {
                "total_documents": len(results),
                "total_triples": total_triples,
                "avg_triples_per_doc": total_triples / len(results),
                "avg_confidence": avg_confidence,
                "avg_entities_per_doc": avg_entities_per_doc,
                "success_rate": valid_extractions / len(results),
                "total_issues": total_issues,
                "total_warnings": total_warnings
            },
            "distributions": {
                "entity_types": all_entity_types,
                "relationship_types": all_relationship_types
            },
            "detailed_metrics": all_metrics,
            "generated_at": datetime.now().isoformat()
        }
    
    def export_to_csv(self, results: List[ExtractionResult], filename: str = None) -> str:
        """
        Export results to CSV for analysis
        
        Args:
            results: List of ExtractionResult objects
            filename: Output filename (optional)
            
        Returns:
            Filename of exported CSV
        """
        if not filename:
            filename = f"extraction_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # Flatten all triples into rows
        rows = []
        for result in results:
            for triple in result.triples:
                row = {
                    "document_id": result.document_id,
                    "extraction_timestamp": result.extraction_timestamp.isoformat(),
                    "entity_subject": triple.entity_subject,
                    "relationship_type": triple.relationship_type,
                    "entity_object": triple.entity_object,
                    "entity_subject_type": triple.entity_subject_type,
                    "entity_object_type": triple.entity_object_type,
                    "confidence_score": triple.confidence_score,
                    "source_text_snippet": triple.source_text_snippet,
                    "extraction_method": triple.extraction_method,
                    "chunk_id": triple.chunk_id,
                    "relationship_context": triple.relationship_context,
                    "entity_subject_id": triple.entity_subject_id,
                    "entity_object_id": triple.entity_object_id
                }
                rows.append(row)
        
        # Create DataFrame and export
        df = pd.DataFrame(rows)
        df.to_csv(filename, index=False)
        
        return filename
    
    def export_to_json(self, results: List[ExtractionResult], filename: str = None) -> str:
        """
        Export results to JSON for analysis
        
        Args:
            results: List of ExtractionResult objects
            filename: Output filename (optional)
            
        Returns:
            Filename of exported JSON
        """
        if not filename:
            filename = f"extraction_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Convert to JSON-serializable format
        json_data = []
        for result in results:
            json_data.append({
                "document_id": result.document_id,
                "extraction_timestamp": result.extraction_timestamp.isoformat(),
                "total_triples": result.total_triples,
                "triples": [
                    {
                        "entity_subject": triple.entity_subject,
                        "relationship_type": triple.relationship_type,
                        "entity_object": triple.entity_object,
                        "entity_subject_type": triple.entity_subject_type,
                        "entity_object_type": triple.entity_object_type,
                        "confidence_score": triple.confidence_score,
                        "source_text_snippet": triple.source_text_snippet,
                        "extraction_method": triple.extraction_method,
                        "chunk_id": triple.chunk_id,
                        "relationship_context": triple.relationship_context,
                        "entity_subject_id": triple.entity_subject_id,
                        "entity_object_id": triple.entity_object_id
                    }
                    for triple in result.triples
                ]
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        return filename