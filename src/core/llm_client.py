"""
Lightweight LLM Client for unified LLM calling
Uses LiteLLM for provider abstraction
"""

import os
from typing import Dict, Any, List, Optional
import litellm
from pydantic import BaseModel, ValidationError


class LLMClient:
    """
    Lightweight client for LLM operations using LiteLLM
    
    Responsibilities:
    - Store LLM configuration
    - Provide unified calling interface
    - Handle provider-specific model formatting
    - Basic error handling and retries
    
    NOT responsible for:
    - Business logic (extraction, summarization, etc.)
    - Schema definitions (belongs in ontology)
    - Response processing (belongs in calling functions)
    """
    
    def __init__(self, llm_config: Dict[str, Any]):
        """
        Initialize LLM client with configuration
        
        Args:
            llm_config: Configuration dict from get_llm_config() with:
                       - provider: "openai", "anthropic", or "ollama"
                       - model: Model name
                       - temperature, max_tokens, etc.
        """
        self.provider = llm_config["provider"]
        self.model_name = llm_config["model"]
        self.temperature = llm_config.get("temperature", 0.1)
        self.max_tokens = llm_config.get("max_tokens", 4000)
        
        # Format model for LiteLLM: "provider/model"
        if self.provider == "openai":
            self.litellm_model = f"openai/{self.model_name}"
        elif self.provider == "anthropic":
            self.litellm_model = f"anthropic/{self.model_name}"
        elif self.provider == "ollama":
            self.litellm_model = f"ollama/{self.model_name}"
            # Set Ollama base URL if provided
            if "base_url" in llm_config:
                os.environ["OLLAMA_API_BASE"] = llm_config["base_url"]
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")

        # Register Ollama models for native function calling support
        if self.provider == "ollama":
            self._register_ollama_model()

        # Ensure API key is set for providers that need it
        api_key_env = llm_config.get("api_key_env")
        if api_key_env:
            api_key = os.getenv(api_key_env)
            if not api_key:
                raise EnvironmentError(f"API key not found: {api_key_env}")
            else:
                print(f"🔑 Found API key for {api_key_env}: {api_key[:10]}...")

                # Set the API key in the environment for LiteLLM
                if self.provider == "anthropic":
                    os.environ["ANTHROPIC_API_KEY"] = api_key
                elif self.provider == "openai":
                    os.environ["OPENAI_API_KEY"] = api_key
    
    def call(self, messages: List[Dict[str, str]], tools: Optional[List[Dict[str, Any]]] = None) -> Any:
        """
        Make LLM call with unified interface
        
        Args:
            messages: Chat messages in OpenAI format
            tools: Optional function calling tools
            
        Returns:
            LiteLLM response object
            
        Raises:
            RuntimeError: If LLM call fails
        """
        try:
            # Build call parameters
            call_params = {
                "model": self.litellm_model,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens
            }
            
            # Add tools if provided
            if tools:
                call_params["tools"] = tools
                call_params["tool_choice"] = "auto"
            
            # Make unified LiteLLM call
            response = litellm.completion(**call_params)
            return response
            
        except Exception as e:
            raise RuntimeError(f"LLM call failed ({self.provider}/{self.model_name}): {str(e)}")

    def _register_ollama_model(self):
        """
        Register Ollama model with LiteLLM for native function calling support

        This tells LiteLLM that the Ollama model supports function calling,
        so it uses Ollama's native tool calling API instead of JSON prompting fallback.
        """
        from litellm.utils import register_model

        # Register the model with function calling capability
        model_cost_map = {
            f"ollama_chat/{self.model_name}": {
                "supports_function_calling": True,
                "supports_parallel_function_calling": True,
                "supports_vision": False,
                "input_cost_per_token": 0.0,  # Ollama is free
                "output_cost_per_token": 0.0,
                "max_tokens": 4096,
                "max_input_tokens": 4096,
                "max_output_tokens": 4096,
                "litellm_provider": "ollama_chat",
                "mode": "chat"
            }
        }

        # Register with LiteLLM
        register_model(model_cost=model_cost_map)
        print(f"🔧 Registered {self.model_name} for native function calling")

    def __repr__(self) -> str:
        return f"LLMClient(provider={self.provider}, model={self.model_name})"


def instantiate_llm(llm_config: Dict[str, Any]) -> LLMClient:
    """
    Factory function to create LLM client from configuration
    
    Args:
        llm_config: Configuration dict from get_llm_config()
        
    Returns:
        Configured LLMClient instance
    """
    return LLMClient(llm_config)
