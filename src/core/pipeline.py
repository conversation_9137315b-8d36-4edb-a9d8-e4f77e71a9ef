"""
Core pipeline functions for graph ingestion
Single-purpose functions following the refactored architecture
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid
import re
import json
import pandas as pd
import os


def row_to_json(row: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert input row to standardized JSON format
    
    Args:
        row: Input row with keys like 'UID', 'freeform_text', etc.
             Expected keys: 'UID' (required), 'freeform_text' (required)
             Optional keys: any additional metadata
    
    Returns:
        Standardized JSON with:
        - document_id: Unique identifier for the document
        - freeform_text: The text content to process
        - metadata: Additional row data
        - processing_timestamp: When standardization occurred
    
    Raises:
        ValueError: If required keys are missing
    """
    # Validate required fields
    if 'UID' not in row:
        raise ValueError("Row must contain 'UID' field")
    if 'freeform_text' not in row:
        raise ValueError("Row must contain 'freeform_text' field")
    
    # Extract core fields
    document_id = str(row['UID'])
    freeform_text = str(row['freeform_text'])
    
    # Collect metadata (everything except core fields)
    metadata = {k: v for k, v in row.items() if k not in ['UID', 'freeform_text']}
    
    # Create standardized JSON
    standardized_json = {
        "document_id": document_id,
        "freeform_text": freeform_text,
        "metadata": metadata,
        "processing_timestamp": datetime.now().isoformat()
    }
    
    return standardized_json


def extract_triples(text_json: Dict[str, Any], llm_config: Dict[str, Any], ontology: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extract entity-relationship triples from standardized text JSON using LLM

    Args:
        text_json: Standardized JSON from row_to_json() with:
                  - document_id: Unique identifier
                  - freeform_text: Text content to process
                  - metadata: Additional data
                  - processing_timestamp: When standardized
        llm_config: LLM configuration from get_llm_config() with:
                   - provider: "openai", "anthropic", or "ollama"
                   - model: Model name
                   - temperature, max_tokens, etc.
        ontology: Parsed ontology dict from load_ontology_from_cypher() with:
                 - entities: Dict of entity types and descriptions
                 - relationships: Dict of relationship types and descriptions

    Returns:
        List of extracted triples with metadata repeated in each:
        [
            {
                "entity_subject": "Sarah Johnson",
                "relationship_type": "holds_role",
                "entity_object": "VP of Engineering",
                "entity_subject_type": "Person",
                "entity_object_type": "Role",
                "confidence_score": 0.85,
                "source_text_snippet": "Sarah Johnson, the VP of Engineering",
                "relationship_context": "Role assignment",
                "extraction_method": "litellm_function_calling",
                "document_id": "DOC_001",
                "chunk_id": "full_text",
                "extraction_timestamp": "2024-01-15T10:30:00",
                "entity_subject_id": "sarah_johnson_abc123",
                "entity_object_id": "vp_engineering_def456"
            },
            {
                "entity_subject": "Sarah Johnson",
                "relationship_type": "works_at",
                "entity_object": "TechCorp",
                "entity_subject_type": "Person",
                "entity_object_type": "Organization",
                "confidence_score": 0.85,
                "source_text_snippet": "Sarah Johnson leads TechCorp's engineering team",
                "relationship_context": "Employment",
                "extraction_method": "litellm_function_calling",
                "document_id": "DOC_001",
                "chunk_id": "full_text",
                "extraction_timestamp": "2024-01-15T10:30:00",
                "entity_subject_id": "sarah_johnson_abc123",
                "entity_object_id": "techcorp_def456"
            }
        ]

    Raises:
        ValueError: If text_json or llm_config is invalid
        RuntimeError: If LLM extraction fails
    """
    import json
    import uuid
    from datetime import datetime
    from pydantic import ValidationError

    # Import LLM client and ontology generators
    from core.llm_client import LLMClient
    from ontology.generators import generate_function_schema, generate_pydantic_enums

    # Validate inputs
    if not isinstance(text_json, dict):
        raise ValueError("text_json must be a dictionary")
    if not isinstance(llm_config, dict):
        raise ValueError("llm_config must be a dictionary")

    required_text_fields = ["document_id", "freeform_text"]
    for field in required_text_fields:
        if field not in text_json:
            raise ValueError(f"text_json missing required field: {field}")

    required_config_fields = ["provider", "model"]
    for field in required_config_fields:
        if field not in llm_config:
            raise ValueError(f"llm_config missing required field: {field}")

    # Extract data
    document_id = text_json["document_id"]
    freeform_text = text_json["freeform_text"]

    try:
        # Generate ontology-specific components
        function_schema = generate_function_schema(ontology)
        EntityType, RelationshipType = generate_pydantic_enums(ontology)

        # Create LLM client using lightweight class
        llm_client = LLMClient(llm_config)

        # Build ontology prompt
        prompt = _build_ontology_prompt(freeform_text, ontology)

        # Make unified LLM call with function calling
        response = llm_client.call(
            messages=[{"role": "user", "content": prompt}],
            tools=[function_schema]
        )

        # Extract function call result
        if response.choices[0].message.tool_calls:
            tool_call = response.choices[0].message.tool_calls[0]
            raw_result = json.loads(tool_call.function.arguments)
        else:
            raise RuntimeError("LLM did not return function call")

        # Handle case where LLM returns JSON string instead of parsed JSON
        if isinstance(raw_result.get("triples"), str):
            try:
                raw_result["triples"] = json.loads(raw_result["triples"])
            except json.JSONDecodeError as e:
                # Try to repair common JSON issues
                repaired_json = _repair_json_string(raw_result["triples"])
                try:
                    raw_result["triples"] = json.loads(repaired_json)
                    print(f"⚠️  JSON repaired successfully")
                except json.JSONDecodeError as e2:
                    raise RuntimeError(f"Failed to parse triples JSON string even after repair: {e2}")

        # Debug: Show what the LLM actually returned
        print(f"🔍 DEBUG - LLM returned: {json.dumps(raw_result, indent=2)}")



        # Validate response structure and ontology adherence
        from pydantic import BaseModel, ValidationError, field_validator
        from typing import List

        # Get valid values from ontology
        valid_entities = list(ontology["entities"].keys())
        valid_relationships = list(ontology["relationships"].keys())

        # Create dynamic validation models with ontology constraints
        class TripleResponse(BaseModel):
            entity_subject: str
            relationship_type: str
            entity_object: str
            entity_subject_type: str
            entity_object_type: str
            source_text_snippet: str
            relationship_context: str

            @field_validator('relationship_type')
            @classmethod
            def validate_relationship_type(cls, v):
                if v not in valid_relationships:
                    raise ValueError(f'relationship_type must be one of {valid_relationships}')
                return v

            @field_validator('entity_subject_type', 'entity_object_type')
            @classmethod
            def validate_entity_types(cls, v):
                if v not in valid_entities:
                    raise ValueError(f'entity type must be one of {valid_entities}')
                return v

        class ExtractionResponse(BaseModel):
            triples: List[TripleResponse]

        # Validate response with dynamic Pydantic models
        try:
            extraction = ExtractionResponse(**raw_result)
        except ValidationError as e:
            raise RuntimeError(f"LLM response validation failed: {e}")

        # Process validated triples into final format
        processed_triples = []
        extraction_timestamp = datetime.now().isoformat()

        for triple in extraction.triples:
            # Generate unique IDs
            subject_id = f"{triple.entity_subject.lower().replace(' ', '_')}_{uuid.uuid4().hex[:8]}"
            object_id = f"{triple.entity_object.lower().replace(' ', '_')}_{uuid.uuid4().hex[:8]}"

            processed_triple = {
                "entity_subject": triple.entity_subject,
                "relationship_type": triple.relationship_type,
                "entity_object": triple.entity_object,
                "entity_subject_type": triple.entity_subject_type,
                "entity_object_type": triple.entity_object_type,
                "confidence_score": 0.85,  # MVP default
                "source_text_snippet": triple.source_text_snippet,
                "relationship_context": triple.relationship_context,
                "extraction_method": "litellm_function_calling",
                "document_id": document_id,
                "chunk_id": "full_text",
                "extraction_timestamp": extraction_timestamp,
                "entity_subject_id": subject_id,
                "entity_object_id": object_id
            }
            processed_triples.append(processed_triple)

        return processed_triples

    except Exception as e:
        raise RuntimeError(f"LLM extraction failed for document {document_id}: {str(e)}")



def _repair_json_string(json_str: str) -> str:
    """
    Attempt to repair common JSON formatting issues from LLM responses

    Args:
        json_str: Potentially malformed JSON string

    Returns:
        Repaired JSON string
    """
    # Common repairs for LLM-generated JSON
    repaired = json_str

    # Fix missing commas between objects
    repaired = re.sub(r'}\s*{', '}, {', repaired)

    # Fix missing commas between array elements
    repaired = re.sub(r'}\s*\]', '}]', repaired)

    # Fix trailing commas before closing brackets
    repaired = re.sub(r',\s*}', '}', repaired)
    repaired = re.sub(r',\s*\]', ']', repaired)

    # Fix missing quotes around keys (basic cases)
    repaired = re.sub(r'(\w+):', r'"\1":', repaired)

    # Fix single quotes to double quotes
    repaired = repaired.replace("'", '"')

    return repaired


def _build_ontology_prompt(text: str, ontology: Dict[str, Any]) -> str:
    """
    Build ontology-aware prompt for entity-relationship extraction with strict adherence

    Args:
        text: Text to analyze
        ontology: Parsed ontology dict with entities and relationships

    Returns:
        Formatted prompt string with strict ontology constraints
    """
    from ontology.generators import generate_prompt_ontology

    ontology_text = generate_prompt_ontology(ontology)

    return f"""
CRITICAL: You MUST strictly adhere to the predefined ontology. Do NOT create new entity types or relationship types.

ONTOLOGY DEFINITION:
{ontology_text}

STRICT EXTRACTION RULES:
1. RELATIONSHIP_TYPE field must be EXACTLY one of the relationship names listed above (e.g., "works_at", "leads", "participates_in")
2. ENTITY_TYPE fields must be EXACTLY one of the entity names listed above (e.g., "Person", "Organization", "Event")
3. NEVER use entity names (like "Event", "Person") as relationship types
4. NEVER use relationship names (like "works_at", "leads") as entity types
5. If you see a relationship that doesn't match the allowed list, skip it entirely

EXAMPLES:
✅ CORRECT: "Sarah attends the meeting"
   → entity_subject: "Sarah", entity_subject_type: "Person"
   → relationship_type: "participates_in"
   → entity_object: "meeting", entity_object_type: "Event"

❌ WRONG: "Sarah attends the meeting"
   → relationship_type: "Event" (Event is an entity type, not a relationship type!)

✅ CORRECT: "The quarterly meeting will be held in Conference Room A"
   → entity_subject: "quarterly meeting", entity_subject_type: "Event"
   → relationship_type: "located_in"
   → entity_object: "Conference Room A", entity_object_type: "Location"

❌ WRONG: "The quarterly meeting will be held in Conference Room A"
   → relationship_type: "Event" (NEVER use entity types as relationships!)

REMEMBER:
- relationship_type must ALWAYS be a VERB or ACTION: "works_at", "participates_in", "located_in"
- entity_subject_type and entity_object_type are NOUNS: "Person", "Event", "Location"

TEXT TO ANALYZE:
{text}

Extract ONLY relationships that exist in the ontology. Use the extract_entities_relationships function to return your results.
"""


def load_text(file_path: str) -> List[Dict[str, Any]]:
    """
    Load sample data from CSV or JSON file

    Args:
        file_path: Path to CSV or JSON file. If None, uses default locations.

    Returns:
        List of dictionaries with UID, freeform_text, and metadata
    """
    from pathlib import Path
    
    if not file_path:
        raise ValueError("file_path must be provided")
        
    if not Path(file_path).exists():
        raise FileNotFoundError(f"File not found: {file_path}")
        
    if file_path.endswith('.csv'):
        return _load_from_csv(file_path)
    elif file_path.endswith('.json'):
        return _load_from_json(file_path)
    else:
        raise ValueError(f"Unsupported file type. Must be .csv or .json: {file_path}")


def _load_from_csv(csv_path: str) -> List[Dict[str, Any]]:
    """Load data from CSV file with flexible column naming"""
    try:
        df = pd.read_csv(csv_path)

        # Check for required columns (flexible naming)
        id_col = None
        text_col = None

        for col in df.columns:
            if col.lower() in ['unique_id', 'uid', 'document_id', 'id']:
                id_col = col
            elif col.lower() in ['text', 'freeform_text', 'content']:
                text_col = col

        if not id_col or not text_col:
            raise ValueError(f"CSV must have ID column (unique_id/uid/document_id/id) and text column (text/freeform_text/content). Found: {list(df.columns)}")

        # Convert to standard format
        data = []
        for _, row in df.iterrows():
            data.append({
                "UID": str(row[id_col]),
                "freeform_text": str(row[text_col]),
                "metadata": {"source": "csv_file", "date": "2024-01-01"}
            })

        print(f"   ✅ Loaded {len(data)} rows from CSV: {csv_path}")
        return data

    except Exception as e:
        print(f"   ❌ Failed to load CSV {csv_path}: {e}")
        return _get_dummy_data()


def _load_from_json(json_path: str) -> List[Dict[str, Any]]:
    """Load data from JSON file (existing format)"""
    try:
        import json
        with open(json_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        # Convert to standard format if needed
        data = []
        for item in json_data:
            if "document_id" in item and "text" in item:
                # Old JSON format
                data.append({
                    "UID": item["document_id"],
                    "freeform_text": item["text"],
                    "metadata": {"source": "json_file", "date": "2024-01-01"}
                })
            elif "UID" in item and "freeform_text" in item:
                # Current format
                data.append(item)
            else:
                print(f"   ⚠️ Skipping invalid JSON item: {item}")

        print(f"   ✅ Loaded {len(data)} rows from JSON: {json_path}")
        return data

    except Exception as e:
        print(f"   ❌ Failed to load JSON {json_path}: {e}")
        return _get_dummy_data()


def _get_dummy_data() -> List[Dict[str, Any]]:
    """Fallback dummy data for testing"""
    return [
        {
            "UID": "DOC_001",
            "freeform_text": "Sarah Johnson, the VP of Engineering at TechCorp, is leading the AI initiative project in collaboration with DataSoft.",
            "metadata": {"source": "dummy_data", "date": "2024-01-15"}
        },
        {
            "UID": "DOC_002",
            "freeform_text": "The quarterly meeting will be held in Conference Room A with all department heads attending.",
            "metadata": {"source": "dummy_data", "date": "2024-01-16"}
        }
    ]


# NOTE: create_llm_config() function has been DEPRECATED
# Use proper config imports instead:
# from config.llm_config import LLM_CONFIGS, get_llm_config


def append_triples_to_dataframe(triples_array: List[Dict[str, Any]], existing_df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
    """
    Convert triples array to pandas DataFrame (simplest version)

    Args:
        triples_array: List of triple dictionaries from extract_triples()
        existing_df: Optional existing DataFrame to append to

    Returns:
        pandas DataFrame with triples data
    """
    if not triples_array:
        # Return empty DataFrame with expected columns if no triples
        columns = ['entity_subject', 'relationship_type', 'entity_object',
                  'entity_subject_type', 'entity_object_type', 'confidence_score',
                  'source_text_snippet', 'relationship_context', 'extraction_method',
                  'document_id', 'chunk_id', 'extraction_timestamp',
                  'entity_subject_id', 'entity_object_id']
        return pd.DataFrame(columns=columns)

    # Convert triples to DataFrame
    df = pd.DataFrame(triples_array)

    # If existing DataFrame provided, concatenate
    if existing_df is not None:
        df = pd.concat([existing_df, df], ignore_index=True)

    return df


def export_dataframe_to_csv(df: pd.DataFrame, output_path: Optional[str] = None) -> str:
    """
    Export DataFrame to CSV file with timestamp in organized directory

    Args:
        df: pandas DataFrame with triples data
        output_path: Optional custom path, otherwise uses timestamped default

    Returns:
        str: Path to the saved file
    """
    if output_path is None:
        # Generate timestamped filename in organized directory
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f"outputs/demo/demo_results_{timestamp}.csv"

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Export to CSV
    df.to_csv(output_path, index=False)

    return output_path